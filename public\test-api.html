<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test API - Winplus FAQ</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold mb-8">Test API Endpoints</h1>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- System Info Test -->
            <div class="bg-white p-6 rounded-lg shadow">
                <h2 class="text-xl font-bold mb-4">System Info</h2>
                <button id="testSystemInfo" class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
                    Test System Info
                </button>
                <pre id="systemInfoResult" class="mt-4 bg-gray-100 p-4 rounded text-sm overflow-auto max-h-40"></pre>
            </div>

            <!-- Backup Test -->
            <div class="bg-white p-6 rounded-lg shadow">
                <h2 class="text-xl font-bold mb-4">Database Backup</h2>
                <button id="testBackup" class="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700">
                    Test Backup
                </button>
                <pre id="backupResult" class="mt-4 bg-gray-100 p-4 rounded text-sm overflow-auto max-h-40"></pre>
            </div>

            <!-- Clear Cache Test -->
            <div class="bg-white p-6 rounded-lg shadow">
                <h2 class="text-xl font-bold mb-4">Clear Cache</h2>
                <button id="testClearCache" class="bg-yellow-600 text-white px-4 py-2 rounded hover:bg-yellow-700">
                    Test Clear Cache
                </button>
                <pre id="clearCacheResult" class="mt-4 bg-gray-100 p-4 rounded text-sm overflow-auto max-h-40"></pre>
            </div>

            <!-- Login Test -->
            <div class="bg-white p-6 rounded-lg shadow">
                <h2 class="text-xl font-bold mb-4">Admin Login</h2>
                <button id="testLogin" class="bg-purple-600 text-white px-4 py-2 rounded hover:bg-purple-700">
                    Test Login
                </button>
                <pre id="loginResult" class="mt-4 bg-gray-100 p-4 rounded text-sm overflow-auto max-h-40"></pre>
            </div>
        </div>

        <div class="mt-8 bg-white p-6 rounded-lg shadow">
            <h2 class="text-xl font-bold mb-4">Console Log</h2>
            <div id="consoleLog" class="bg-black text-green-400 p-4 rounded font-mono text-sm h-64 overflow-auto"></div>
        </div>
    </div>

    <script>
        // Get base URL for API calls
        function getApiUrl(endpoint) {
            const host = window.location.hostname;
            const port = '3000';
            return `http://${host}:${port}${endpoint}`;
        }

        // Console logging
        function log(message, type = 'info') {
            const consoleLog = document.getElementById('consoleLog');
            const timestamp = new Date().toLocaleTimeString();
            const color = type === 'error' ? 'text-red-400' : type === 'success' ? 'text-green-400' : 'text-blue-400';
            consoleLog.innerHTML += `<div class="${color}">[${timestamp}] ${message}</div>`;
            consoleLog.scrollTop = consoleLog.scrollHeight;
        }

        // Utility function to ensure we're logged in
        async function ensureLoggedIn() {
            try {
                // Test if we're already logged in by trying to access a protected endpoint
                const testResponse = await fetch(getApiUrl('/api/admin/system-info'), {
                    credentials: 'include'
                });

                if (testResponse.status === 401) {
                    log('🔐 Non connecté, connexion automatique...');
                    await testLogin();
                } else {
                    log('✅ Déjà connecté');
                }
            } catch (error) {
                log('🔐 Erreur vérification connexion, tentative de connexion...');
                await testLogin();
            }
        }

        // Test functions
        async function testSystemInfo() {
            log('Testing System Info API...');
            try {
                const response = await fetch(getApiUrl('/api/admin/system-info'));
                const result = await response.json();
                
                document.getElementById('systemInfoResult').textContent = JSON.stringify(result, null, 2);
                
                if (result.success) {
                    log('✅ System Info API working!', 'success');
                } else {
                    log('❌ System Info API failed: ' + result.message, 'error');
                }
            } catch (error) {
                log('❌ System Info API error: ' + error.message, 'error');
                document.getElementById('systemInfoResult').textContent = 'Error: ' + error.message;
            }
        }

        async function testBackup() {
            log('Testing Backup API...');

            // D'abord, s'assurer qu'on est connecté
            log('🔐 Vérification de la connexion admin...');
            await ensureLoggedIn();

            try {
                const response = await fetch(getApiUrl('/api/admin/backup'), {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    credentials: 'include' // Important pour inclure les cookies de session
                });
                const result = await response.json();

                document.getElementById('backupResult').textContent = JSON.stringify(result, null, 2);

                if (result.success) {
                    log('✅ Backup API working!', 'success');
                    log(`📄 Fichier créé: ${result.filename} (${result.size} bytes)`);
                } else {
                    log('❌ Backup API failed: ' + result.message, 'error');
                    if (response.status === 401) {
                        log('🔐 Problème d\'authentification - tentative de reconnexion...');
                        await testLogin();
                    }
                }
            } catch (error) {
                log('❌ Backup API error: ' + error.message, 'error');
                document.getElementById('backupResult').textContent = 'Error: ' + error.message;
            }
        }

        async function testClearCache() {
            log('Testing Clear Cache API...');

            // D'abord, s'assurer qu'on est connecté
            log('🔐 Vérification de la connexion admin...');
            await ensureLoggedIn();

            try {
                const response = await fetch(getApiUrl('/api/admin/clear-cache'), {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    credentials: 'include' // Important pour inclure les cookies de session
                });
                const result = await response.json();

                document.getElementById('clearCacheResult').textContent = JSON.stringify(result, null, 2);

                if (result.success) {
                    log('✅ Clear Cache API working!', 'success');
                    log(`🧹 Éléments supprimés: ${result.clearedItems}`);
                } else {
                    log('❌ Clear Cache API failed: ' + result.message, 'error');
                    if (response.status === 401) {
                        log('🔐 Problème d\'authentification - tentative de reconnexion...');
                        await testLogin();
                    }
                }
            } catch (error) {
                log('❌ Clear Cache API error: ' + error.message, 'error');
                document.getElementById('clearCacheResult').textContent = 'Error: ' + error.message;
            }
        }

        async function testLogin() {
            log('Testing Login API...');
            try {
                const response = await fetch(getApiUrl('/api/admin/login'), {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    credentials: 'include', // Important pour sauvegarder les cookies de session
                    body: JSON.stringify({
                        username: 'admin',
                        password: 'Sophatel@9999'
                    })
                });
                const result = await response.json();

                document.getElementById('loginResult').textContent = JSON.stringify(result, null, 2);

                if (result.success) {
                    log('✅ Login API working!', 'success');
                    log('🍪 Session créée, vous pouvez maintenant tester les autres APIs');
                } else {
                    log('❌ Login API failed: ' + result.message, 'error');
                }
            } catch (error) {
                log('❌ Login API error: ' + error.message, 'error');
                document.getElementById('loginResult').textContent = 'Error: ' + error.message;
            }
        }

        // Event listeners
        document.getElementById('testSystemInfo').addEventListener('click', testSystemInfo);
        document.getElementById('testBackup').addEventListener('click', testBackup);
        document.getElementById('testClearCache').addEventListener('click', testClearCache);
        document.getElementById('testLogin').addEventListener('click', testLogin);

        // Initial log
        log('API Test Page loaded. Click buttons to test endpoints.');
        log('Current API base URL: ' + getApiUrl(''));
    </script>
</body>
</html>
