// Main FAQ Form JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // Fonction pour afficher les notifications
    function showNotification(type, title, text) {
        if (typeof Swal !== 'undefined') {
            // Utiliser SweetAlert si disponible
            if (type === 'success') {
                Swal.fire({
                    icon: 'success',
                    title: title,
                    text: text,
                    confirmButtonText: 'Parfait !',
                    confirmButtonColor: '#1a73e8',
                    timer: 5000,
                    timerProgressBar: true,
                    showClass: {
                        popup: 'animate__animated animate__fadeInDown'
                    },
                    hideClass: {
                        popup: 'animate__animated animate__fadeOutUp'
                    }
                });
            } else if (type === 'error') {
                Swal.fire({
                    icon: 'error',
                    title: title,
                    text: text,
                    confirmButtonText: 'Réessayer',
                    confirmButtonColor: '#ea4335',
                    showClass: {
                        popup: 'animate__animated animate__shakeX'
                    },
                    hideClass: {
                        popup: 'animate__animated animate__fadeOutUp'
                    }
                });
            }
        } else {
            // Fallback vers alert natif
            alert(title + '\n' + text);
        }
    }
    const form = document.getElementById('faqForm');
    const addQuestionBtn = document.getElementById('addQuestionBtn');
    const questionsContainer = document.getElementById('questionsContainer');
    const questionTextarea = document.getElementById('questionTextarea');
    const questionsData = document.getElementById('questionsData');
    const submitBtn = document.getElementById('submitBtn');

    // Blocked questions elements
    const addBlockedQuestionBtn = document.getElementById('addBlockedQuestionBtn');
    const blockedQuestionsContainer = document.getElementById('blockedQuestionsContainer');
    const blockedQuestionTextarea = document.getElementById('blockedQuestionTextarea');
    const blockedQuestionsData = document.getElementById('blockedQuestionsData');

    let questions = []; // Array to store regular questions
    let blockedQuestions = []; // Array to store blocked questions

    // Add question as badge
    addQuestionBtn.addEventListener('click', function() {
        const questionText = questionTextarea.value.trim();

        // Validation améliorée
        if (!questionText) {
            showFieldError(questionTextarea, 'Veuillez entrer une question');
            return;
        }

        if (questionText.length < 10) {
            showFieldError(questionTextarea, 'La question doit contenir au moins 10 caractères');
            return;
        }

        if (questionText.length > 1000) {
            showFieldError(questionTextarea, 'La question ne peut pas dépasser 1000 caractères');
            return;
        }

        // Vérifier que ce n'est pas juste de la ponctuation ou des caractères spéciaux
        const meaningfulText = questionText.replace(/[^\w\s]/gi, '').trim();
        if (meaningfulText.length < 5) {
            showFieldError(questionTextarea, 'Veuillez entrer une question plus détaillée');
            return;
        }

        // Add question to array
        const questionId = Date.now();
        questions.push({
            id: questionId,
            text: questionText
        });

        // Create badge
        createQuestionBadge(questionId, questionText);

        // Clear textarea
        questionTextarea.value = '';
        clearFieldError(questionTextarea);

        // Update hidden input
        updateQuestionsData();

        // Update button text
        updateAddButtonText();
    });

    // Add blocked question event listener
    addBlockedQuestionBtn.addEventListener('click', function() {
        const questionText = blockedQuestionTextarea.value.trim();

        // Validation
        if (!questionText) {
            showFieldError(blockedQuestionTextarea, 'Veuillez entrer une question à bloquer');
            return;
        }

        if (questionText.length < 10) {
            showFieldError(blockedQuestionTextarea, 'La question doit contenir au moins 10 caractères');
            return;
        }

        if (questionText.length > 1000) {
            showFieldError(blockedQuestionTextarea, 'La question ne peut pas dépasser 1000 caractères');
            return;
        }

        // Add blocked question to array
        const questionId = Date.now();
        blockedQuestions.push({
            id: questionId,
            text: questionText
        });

        // Create badge
        createBlockedQuestionBadge(questionId, questionText);

        // Clear textarea
        blockedQuestionTextarea.value = '';
        clearFieldError(blockedQuestionTextarea);

        // Update hidden input
        updateBlockedQuestionsData();

        // Update button text
        updateAddBlockedButtonText();
    });

    // Create question badge (Google Forms style)
    function createQuestionBadge(questionId, questionText) {
        const badgeDiv = document.createElement('div');
        badgeDiv.className = 'question-badge-google';
        badgeDiv.dataset.questionId = questionId;

        const truncatedText = questionText.length > 150 ? questionText.substring(0, 150) + '...' : questionText;

        badgeDiv.innerHTML = `
            <div class="question-text">
                ${escapeHtml(truncatedText)}
                ${questionText.length > 150 ? `<button type="button" class="text-google-blue text-xs mt-1 hover:underline" data-question-id="${questionId}" data-action="toggle">Voir plus</button>` : ''}
            </div>
            <div class="actions">
                <button type="button" class="action-btn edit-btn" title="Modifier" data-question-id="${questionId}" data-action="edit">
                    <i class="fas fa-edit text-xs"></i>
                </button>
                <button type="button" class="action-btn delete-btn" title="Supprimer" data-question-id="${questionId}" data-action="delete">
                    <i class="fas fa-trash text-xs"></i>
                </button>
            </div>
        `;

        // Add event listeners to the buttons
        const editBtn = badgeDiv.querySelector('.edit-btn');
        const deleteBtn = badgeDiv.querySelector('.delete-btn');
        const toggleBtn = badgeDiv.querySelector('[data-action="toggle"]');

        editBtn.addEventListener('click', () => editQuestion(questionId));
        deleteBtn.addEventListener('click', () => deleteQuestion(questionId));
        if (toggleBtn) {
            toggleBtn.addEventListener('click', () => toggleQuestionText(questionId));
        }

        questionsContainer.appendChild(badgeDiv);
    }

    // Update questions data in hidden input
    function updateQuestionsData() {
        questionsData.value = JSON.stringify(questions.map(q => q.text));
    }

    // Update add button text (Google style)
    function updateAddButtonText() {
        const count = questions.length;
        if (count === 0) {
            addQuestionBtn.innerHTML = '<i class="fas fa-plus text-xs"></i><span>Ajouter cette question</span>';
        } else {
            addQuestionBtn.innerHTML = `<i class="fas fa-plus text-xs"></i><span>Ajouter une autre question (${count})</span>`;
        }

        // No limit on questions
        addQuestionBtn.disabled = false;
        addQuestionBtn.classList.remove('opacity-50', 'cursor-not-allowed');
    }

    // Functions for question management (no longer global to avoid conflicts)
    function editQuestion(questionId) {
        const question = questions.find(q => q.id === questionId);
        if (question) {
            questionTextarea.value = question.text;
            deleteQuestion(questionId);
            questionTextarea.focus();
        }
    }

    function deleteQuestion(questionId) {
        questions = questions.filter(q => q.id !== questionId);
        const badgeElement = document.querySelector(`[data-question-id="${questionId}"]`);
        if (badgeElement) {
            badgeElement.remove();
        }
        updateQuestionsData();
        updateAddButtonText();
    }

    function toggleQuestionText(questionId) {
        const question = questions.find(q => q.id === questionId);
        const badgeElement = document.querySelector(`[data-question-id="${questionId}"]`);
        const textElement = badgeElement.querySelector('p');
        const toggleBtn = badgeElement.querySelector('[data-action="toggle"]');

        if (toggleBtn && toggleBtn.textContent === 'Voir plus') {
            textElement.innerHTML = escapeHtml(question.text);
            toggleBtn.textContent = 'Voir moins';
        } else if (toggleBtn) {
            const truncatedText = question.text.length > 100 ? question.text.substring(0, 100) + '...' : question.text;
            textElement.innerHTML = escapeHtml(truncatedText);
            toggleBtn.textContent = 'Voir plus';
        }
    }

    // Blocked Questions Functions
    function createBlockedQuestionBadge(questionId, questionText) {
        const truncatedText = questionText.length > 100 ? questionText.substring(0, 100) + '...' : questionText;
        const showToggle = questionText.length > 100;

        const badge = document.createElement('div');
        badge.className = 'bg-red-50 border border-red-200 rounded-lg p-4 flex items-start justify-between group hover:shadow-sm transition-shadow';
        badge.setAttribute('data-blocked-question-id', questionId);

        badge.innerHTML = `
            <div class="flex-1 min-w-0">
                <div class="flex items-center mb-2">
                    <i class="fas fa-ban text-red-500 mr-2 text-sm"></i>
                    <span class="text-sm font-medium text-red-700">Question bloquée</span>
                </div>
                <p class="text-sm text-gray-700 leading-relaxed">${escapeHtml(truncatedText)}</p>
                ${showToggle ? `<button type="button" class="text-red-600 hover:text-red-800 text-xs mt-1 underline" data-action="toggle-blocked">Voir plus</button>` : ''}
            </div>
            <div class="flex items-center space-x-2 ml-4 opacity-0 group-hover:opacity-100 transition-opacity">
                <button type="button" class="text-red-600 hover:text-red-800 p-1" data-action="edit-blocked" title="Modifier">
                    <i class="fas fa-edit text-xs"></i>
                </button>
                <button type="button" class="text-red-600 hover:text-red-800 p-1" data-action="delete-blocked" title="Supprimer">
                    <i class="fas fa-trash text-xs"></i>
                </button>
            </div>
        `;

        // Add event listeners
        badge.querySelector('[data-action="edit-blocked"]').addEventListener('click', () => editBlockedQuestion(questionId));
        badge.querySelector('[data-action="delete-blocked"]').addEventListener('click', () => deleteBlockedQuestion(questionId));

        if (showToggle) {
            badge.querySelector('[data-action="toggle-blocked"]').addEventListener('click', () => toggleBlockedQuestionText(questionId));
        }

        blockedQuestionsContainer.appendChild(badge);
    }

    function updateBlockedQuestionsData() {
        blockedQuestionsData.value = JSON.stringify(blockedQuestions.map(q => q.text));
    }

    function updateAddBlockedButtonText() {
        const count = blockedQuestions.length;
        if (count === 0) {
            addBlockedQuestionBtn.innerHTML = '<i class="fas fa-ban text-xs"></i><span>Ajouter cette question bloquée</span>';
        } else {
            addBlockedQuestionBtn.innerHTML = `<i class="fas fa-ban text-xs"></i><span>Ajouter une autre question bloquée (${count})</span>`;
        }
    }

    function editBlockedQuestion(questionId) {
        const question = blockedQuestions.find(q => q.id === questionId);
        if (question) {
            blockedQuestionTextarea.value = question.text;
            deleteBlockedQuestion(questionId);
            blockedQuestionTextarea.focus();
        }
    }

    function deleteBlockedQuestion(questionId) {
        blockedQuestions = blockedQuestions.filter(q => q.id !== questionId);
        const badgeElement = document.querySelector(`[data-blocked-question-id="${questionId}"]`);
        if (badgeElement) {
            badgeElement.remove();
        }
        updateBlockedQuestionsData();
        updateAddBlockedButtonText();
    }

    function toggleBlockedQuestionText(questionId) {
        const question = blockedQuestions.find(q => q.id === questionId);
        const badgeElement = document.querySelector(`[data-blocked-question-id="${questionId}"]`);
        const textElement = badgeElement.querySelector('p');
        const toggleBtn = badgeElement.querySelector('[data-action="toggle-blocked"]');

        if (toggleBtn && toggleBtn.textContent === 'Voir plus') {
            textElement.innerHTML = escapeHtml(question.text);
            toggleBtn.textContent = 'Voir moins';
        } else if (toggleBtn) {
            const truncatedText = question.text.length > 100 ? question.text.substring(0, 100) + '...' : question.text;
            textElement.innerHTML = escapeHtml(truncatedText);
            toggleBtn.textContent = 'Voir plus';
        }
    }

    // Form submission
    form.addEventListener('submit', async function(e) {
        e.preventDefault();

        // Show loading state
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Soumission...';
        submitBtn.disabled = true;
        submitBtn.classList.add('opacity-75');

        try {
            // Collect form data
            const formData = new FormData(form);

            // Validation côté client
            const userName = formData.get('userName');
            const userFunction = formData.get('userFunction');

            if (!userName || userName.trim().length < 2) {
                throw new Error('Le nom complet est requis (minimum 2 caractères).');
            }

            if (!userFunction || userFunction.trim() === '') {
                throw new Error('Veuillez sélectionner votre fonction.');
            }

            // Check if we have any questions to submit
            let hasRegularQuestions = questions.length > 0;
            let hasBlockedQuestions = blockedQuestions.length > 0;

            // Auto-add current questions if they exist
            const currentQuestion = questionTextarea.value.trim();
            if (currentQuestion && currentQuestion.length >= 10) {
                addQuestionBtn.click();
                hasRegularQuestions = true;
            }

            const currentBlockedQuestion = blockedQuestionTextarea.value.trim();
            if (currentBlockedQuestion && currentBlockedQuestion.length >= 10) {
                addBlockedQuestionBtn.click();
                hasBlockedQuestions = true;
            }

            if (!hasRegularQuestions && !hasBlockedQuestions) {
                throw new Error('Veuillez entrer au moins une question (normale ou bloquée).');
            }

            // Submit regular questions if any
            if (hasRegularQuestions) {
                const regularData = {
                    user_name: formData.get('userName'),
                    user_email: formData.get('userEmail'),
                    user_phone: formData.get('userPhone'),
                    user_company: formData.get('userCompany'),
                    user_function: formData.get('userFunction'),
                    question_type: 'regular',
                    questions: questions.map(q => q.text)
                };

                const regularResponse = await fetch('/api/questions', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(regularData)
                });

                if (!regularResponse.ok) {
                    const errorData = await regularResponse.json();
                    throw new Error(errorData.message || 'Erreur lors de la soumission des questions normales');
                }
            }

            // Submit blocked questions if any
            if (hasBlockedQuestions) {
                const blockedData = {
                    user_name: formData.get('userName'),
                    user_email: formData.get('userEmail'),
                    user_phone: formData.get('userPhone'),
                    user_company: formData.get('userCompany'),
                    user_function: formData.get('userFunction'),
                    question_type: 'blocked',
                    questions: blockedQuestions.map(q => q.text)
                };

                const blockedResponse = await fetch('/api/questions', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(blockedData)
                });

                if (!blockedResponse.ok) {
                    const errorData = await blockedResponse.json();
                    throw new Error(errorData.message || 'Erreur lors de la soumission des questions bloquées');
                }
            }

            // Show success notification
            let successMessage = 'Merci pour votre contribution !';
            if (hasRegularQuestions && hasBlockedQuestions) {
                successMessage = 'Vos questions normales et bloquées ont été envoyées avec succès !';
            } else if (hasRegularQuestions) {
                successMessage = 'Vos questions ont été envoyées avec succès !';
            } else if (hasBlockedQuestions) {
                successMessage = 'Vos questions bloquées ont été envoyées avec succès !';
            }

            showNotification(
                'success',
                'Questions envoyées avec succès !',
                successMessage
            );

            // Reset form
            form.reset();

            // Reset questions
            questions = [];
            questionsContainer.innerHTML = '';
            questionTextarea.value = '';
            updateQuestionsData();
            updateAddButtonText();

            // Reset blocked questions
            blockedQuestions = [];
            blockedQuestionsContainer.innerHTML = '';
            blockedQuestionTextarea.value = '';
            updateBlockedQuestionsData();
            updateAddBlockedButtonText();
        } catch (error) {
            console.error('Error submitting questions:', error);

            // Show error notification with specific details
            showNotification(
                'error',
                'Erreur lors de l\'envoi',
                error.message || 'Une erreur est survenue. Veuillez réessayer plus tard.'
            );
        } finally {
            // Restore button state
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
            submitBtn.classList.remove('opacity-75');
        }
    });

    // Auto-resize textareas
    questionTextarea.addEventListener('input', function() {
        this.style.height = 'auto';
        this.style.height = this.scrollHeight + 'px';

        // Remove error styling when user starts typing
        if (this.classList.contains('border-red-500')) {
            clearFieldError(this);
        }
    });

    // Enter key to add question (Shift+Enter for new line)
    questionTextarea.addEventListener('keydown', function(e) {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            addQuestionBtn.click();
        }
    });

    // Utility function to escape HTML
    function escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    // Show field error
    function showFieldError(field, message) {
        // Remove existing error
        clearFieldError(field);

        // Add error styling
        field.classList.add('border-red-500', 'focus:ring-red-500');
        field.classList.remove('border-google-border', 'focus:ring-google-blue');

        // Create error message
        const errorDiv = document.createElement('div');
        errorDiv.className = 'field-error text-red-600 text-sm mt-1';
        errorDiv.textContent = message;

        // Insert after field
        field.parentNode.insertBefore(errorDiv, field.nextSibling);

        // Focus field
        field.focus();
    }

    // Clear field error
    function clearFieldError(field) {
        // Remove error styling
        field.classList.remove('border-red-500', 'focus:ring-red-500');
        field.classList.add('border-google-border', 'focus:ring-google-blue');

        // Remove error message
        const errorDiv = field.parentNode.querySelector('.field-error');
        if (errorDiv) {
            errorDiv.remove();
        }
    }

    // Form validation
    const inputs = form.querySelectorAll('input, textarea, select');
    inputs.forEach(input => {
        input.addEventListener('blur', function() {
            if (this.hasAttribute('required')) {
                validateField(this);
            }
        });

        input.addEventListener('input', function() {
            if (this.classList.contains('border-red-500')) {
                clearFieldError(this);
            }
        });

        input.addEventListener('change', function() {
            if (this.classList.contains('border-red-500')) {
                clearFieldError(this);
            }
        });
    });

    function validateField(field) {
        const isValid = field.checkValidity();

        if (isValid) {
            field.classList.remove('border-red-500');
            field.classList.add('border-green-500');
        } else {
            field.classList.remove('border-green-500');
            field.classList.add('border-red-500');
        }
    }

    // Email validation
    const emailInput = document.getElementById('userEmail');
    emailInput.addEventListener('input', function() {
        const email = this.value;
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        
        if (email && !emailRegex.test(email)) {
            this.setCustomValidity('Please enter a valid email address');
        } else {
            this.setCustomValidity('');
        }
    });

    // Phone number formatting (optional) - French format
    const phoneInput = document.getElementById('userPhone');
    phoneInput.addEventListener('input', function() {
        let value = this.value.replace(/\D/g, '');
        if (value.length >= 10) {
            // French phone format: 01 23 45 67 89
            value = value.replace(/(\d{2})(\d{2})(\d{2})(\d{2})(\d{2})/, '$1 $2 $3 $4 $5');
        }
        this.value = value;
    });

    // Smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // Add some interactive animations
    const questionItems = document.querySelectorAll('.question-item');
    questionItems.forEach(item => {
        item.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
        });
        
        item.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });
});
