<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Winplus FAQ - Soumettez vos Questions</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Google+Sans:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" rel="stylesheet">
    <!-- SweetAlert2 Local -->
    <link href="css/sweetalert2.min.css" rel="stylesheet">
    <script src="js/sweetalert2.min.js"></script>
    <link href="css/styles.css" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'google': ['Google Sans', 'Inter', 'sans-serif'],
                    },
                    colors: {
                        google: {
                            blue: '#1a73e8',
                            'blue-hover': '#1557b0',
                            gray: '#5f6368',
                            'light-gray': '#f8f9fa',
                            border: '#dadce0'
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-white font-google min-h-screen">
    <!-- Clean Header -->
    <header class="bg-white border-b border-gray-200">
        <div class="max-w-4xl mx-auto px-6 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-google-blue rounded-lg flex items-center justify-center">
                        <i class="fas fa-pills text-white text-sm"></i>
                    </div>
                    <div>
                        <h1 class="text-lg font-medium text-gray-900">Winplus FAQ</h1>
                    </div>
                </div>
                <a href="login.html" class="text-google-gray hover:text-google-blue text-sm transition-colors">
                    Admin
                </a>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="bg-google-light-gray min-h-screen py-8">
        <div class="max-w-2xl mx-auto px-6">
            <!-- Form Header -->
            <div class="bg-white rounded-lg shadow-sm border border-google-border mb-6">
                <div class="p-8 border-b border-google-border">
                    <h1 class="text-2xl font-normal text-gray-900 mb-2">Questions FAQ WinPlus Pharma</h1>
                    <p class="text-google-gray text-sm">
                        Aidez-nous à améliorer notre assistant IA en soumettant vos questions sur la plateforme WinPlus Pharma.
                    </p>
                </div>
            </div>

            <!-- Form Content -->
            <form id="faqForm" class="space-y-6">
                <!-- User Information Section -->
                <div class="bg-white rounded-lg shadow-sm border border-google-border">
                    <div class="p-6">
                        <h2 class="text-lg font-normal text-gray-900 mb-6">Informations personnelles</h2>

                        <!-- Name Field -->
                        <div class="mb-6">
                            <label for="userName" class="block text-sm font-medium text-gray-700 mb-2">
                                Nom complet <span class="text-red-500">*</span>
                            </label>
                            <input type="text" id="userName" name="userName" required
                                class="w-full px-3 py-2 border border-google-border rounded-md focus:outline-none focus:ring-2 focus:ring-google-blue focus:border-transparent transition-all"
                                placeholder="Votre nom complet">
                        </div>

                        <!-- Email Field -->
                        <div class="mb-6">
                            <label for="userEmail" class="block text-sm font-medium text-gray-700 mb-2">
                                Adresse email
                            </label>
                            <input type="email" id="userEmail" name="userEmail"
                                class="w-full px-3 py-2 border border-google-border rounded-md focus:outline-none focus:ring-2 focus:ring-google-blue focus:border-transparent transition-all"
                                placeholder="<EMAIL>">
                        </div>

                        <!-- Phone Field -->
                        <div class="mb-6">
                            <label for="userPhone" class="block text-sm font-medium text-gray-700 mb-2">
                                Numéro de téléphone
                            </label>
                            <input type="tel" id="userPhone" name="userPhone"
                                class="w-full px-3 py-2 border border-google-border rounded-md focus:outline-none focus:ring-2 focus:ring-google-blue focus:border-transparent transition-all"
                                placeholder="+*********** 789">
                        </div>

                        <!-- Company Field -->
                        <div class="mb-6">
                            <label for="userCompany" class="block text-sm font-medium text-gray-700 mb-2">
                                Nom de la pharmacie/entreprise
                            </label>
                            <input type="text" id="userCompany" name="userCompany"
                                class="w-full px-3 py-2 border border-google-border rounded-md focus:outline-none focus:ring-2 focus:ring-google-blue focus:border-transparent transition-all"
                                placeholder="Nom de votre pharmacie ou entreprise">
                        </div>

                        <!-- Function Field -->
                        <div class="mb-0">
                            <label for="userFunction" class="block text-sm font-medium text-gray-700 mb-2">
                                Fonction <span class="text-red-500">*</span>
                            </label>
                            <select id="userFunction" name="userFunction" required
                                class="w-full px-3 py-2 border border-google-border rounded-md focus:outline-none focus:ring-2 focus:ring-google-blue focus:border-transparent transition-all bg-white appearance-none cursor-pointer">
                                <option value="">Sélectionnez votre fonction</option>
                                <option value="pharmacien">Pharmacien</option>
                                <option value="commercial">Commercial</option>
                                <option value="team_sophatel">Équipe Sophatel</option>
                                <option value="autres">Autres</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- Questions Section -->
                <div class="bg-white rounded-lg shadow-sm border border-google-border">
                    <div class="p-6">
                        <h2 class="text-lg font-normal text-gray-900 mb-2">Vos questions</h2>
                        <p class="text-sm text-google-gray mb-6">
                            Ajoutez vos questions sur la plateforme Winplus. Vous pouvez ajouter plusieurs questions.
                        </p>

                        <!-- Current Question Input -->
                        <div id="currentQuestionInput" class="mb-4">
                            <label for="questionTextarea" class="block text-sm font-medium text-gray-700 mb-2">
                                Question <span class="text-red-500">*</span>
                            </label>
                            <textarea id="questionTextarea" rows="3"
                                class="w-full px-3 py-2 border border-google-border rounded-md focus:outline-none focus:ring-2 focus:ring-google-blue focus:border-transparent transition-all resize-none"
                                placeholder="Entrez votre question sur la plateforme Winplus..."></textarea>
                            <button type="button" id="addQuestionBtn"
                                class="mt-3 inline-flex items-center space-x-2 px-4 py-2 bg-google-blue text-white rounded-md hover:bg-google-blue-hover transition-colors text-sm">
                                <i class="fas fa-plus text-xs"></i>
                                <span>Ajouter cette question</span>
                            </button>
                        </div>

                        <!-- Questions Badges Container -->
                        <div id="questionsContainer" class="space-y-2">
                            <!-- Questions will be displayed as badges here -->
                        </div>

                        <!-- Hidden input to store questions for form submission -->
                        <input type="hidden" id="questionsData" name="questionsData" value="[]">
                    </div>
                </div>

                <!-- Submit Button -->
                <div class="flex justify-between items-center pt-4">
                    <div></div>
                    <button type="submit" id="submitBtn"
                        class="inline-flex items-center space-x-2 px-6 py-2 bg-google-blue text-white rounded-md hover:bg-google-blue-hover transition-colors font-medium">
                        <span>Envoyer</span>
                        <i class="fas fa-arrow-right text-xs"></i>
                    </button>
                </div>
            </form>


        </div>
    </main>

    <!-- Small Footer -->
    <footer class="bg-white border-t border-gray-200 py-4">
        <div class="max-w-2xl mx-auto px-6 text-center">
            <p class="text-sm text-gray-600">
                Winplus Pharma © 2025<br>
                <b>Sophatel Ingénierie</b> -  
                Tous droits réservés.
            </p>
        </div>
    </footer>

    <!-- Chargement SweetAlert avant le script principal -->
    <script>
        // Vérifier que SweetAlert est chargé
        if (typeof Swal === 'undefined') {
            console.error('SweetAlert2 not loaded!');
            // Fallback vers alert natif
            window.Swal = {
                fire: function(options) {
                    if (options.icon === 'success') {
                        alert(options.title + '\n' + options.text);
                    } else if (options.icon === 'error') {
                        alert('Erreur: ' + options.text);
                    }
                }
            };
        } else {
            console.log('SweetAlert2 loaded successfully');
        }
    </script>
    <script src="js/main.js"></script>
</body>
</html>
