{"name": "winplus-faq", "version": "1.0.0", "description": "Winplus FAQ Application with Admin Dashboard", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "init-db": "node scripts/init-db.js"}, "keywords": ["faq", "winplus", "pharmacy", "management", "admin", "dashboard"], "author": "Winplus Team", "license": "MIT", "dependencies": {"bcryptjs": "^2.4.3", "compression": "^1.7.4", "connect-sqlite3": "^0.9.13", "cors": "^2.8.5", "exceljs": "^4.4.0", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-session": "^1.17.3", "express-validator": "^7.0.1", "helmet": "^7.1.0", "morgan": "^1.10.0", "node-fetch": "^2.7.0", "sqlite3": "^5.1.6"}, "devDependencies": {"nodemon": "^3.0.2"}, "engines": {"node": ">=16.0.0"}}