const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const fs = require('fs');

console.log('🔄 Starting database migration...');

// Ensure data directory exists
const dataDir = path.join(__dirname, '..', 'data');
if (!fs.existsSync(dataDir)) {
    fs.mkdirSync(dataDir, { recursive: true });
    console.log('📁 Created data directory');
}

const dbPath = path.join(dataDir, 'faq.db');

// Check if database exists
if (!fs.existsSync(dbPath)) {
    console.log('❌ Database file not found. Please run init-db.js first.');
    process.exit(1);
}

const db = new sqlite3.Database(dbPath);

console.log('📋 Checking current database schema...');

// Function to check if column exists
function checkColumnExists(tableName, columnName) {
    return new Promise((resolve, reject) => {
        db.all(`PRAGMA table_info(${tableName})`, (err, rows) => {
            if (err) {
                reject(err);
                return;
            }
            
            const columnExists = rows.some(row => row.name === columnName);
            resolve(columnExists);
        });
    });
}

// Function to add missing column
function addColumn(tableName, columnName, columnType) {
    return new Promise((resolve, reject) => {
        const sql = `ALTER TABLE ${tableName} ADD COLUMN ${columnName} ${columnType}`;
        db.run(sql, (err) => {
            if (err) {
                reject(err);
            } else {
                console.log(`✅ Added column ${columnName} to ${tableName} table`);
                resolve();
            }
        });
    });
}

// Main migration function
async function runMigration() {
    try {
        // Check if user_function column exists in questions table
        const userFunctionExists = await checkColumnExists('questions', 'user_function');
        
        if (!userFunctionExists) {
            console.log('🔧 Adding missing user_function column...');
            await addColumn('questions', 'user_function', 'TEXT');
        } else {
            console.log('✅ user_function column already exists');
        }
        
        // Verify the schema
        console.log('📊 Current questions table schema:');
        db.all('PRAGMA table_info(questions)', (err, rows) => {
            if (err) {
                console.error('❌ Error checking schema:', err);
            } else {
                rows.forEach(row => {
                    console.log(`   - ${row.name}: ${row.type} ${row.notnull ? 'NOT NULL' : ''} ${row.dflt_value ? `DEFAULT ${row.dflt_value}` : ''}`);
                });
            }
            
            console.log('\n🎉 Database migration completed successfully!');
            console.log('✅ The database is now compatible with the current application version.');
            
            db.close((err) => {
                if (err) {
                    console.error('❌ Error closing database:', err);
                } else {
                    console.log('📝 Database connection closed.');
                }
                process.exit(0);
            });
        });
        
    } catch (error) {
        console.error('❌ Migration failed:', error);
        db.close();
        process.exit(1);
    }
}

// Run the migration
runMigration();
