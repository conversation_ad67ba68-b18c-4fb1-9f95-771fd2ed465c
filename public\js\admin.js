// Admin Dashboard JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // DOM elements
    const logoutBtn = document.getElementById('logoutBtn');
    const refreshBtn = document.getElementById('refreshBtn');
    const exportBtn = document.getElementById('exportBtn');
    const searchInput = document.getElementById('searchInput');
    const statusFilter = document.getElementById('statusFilter');
    const userFilter = document.getElementById('userFilter');
    const viewMode = document.getElementById('viewMode');
    const clearFiltersBtn = document.getElementById('clearFilters');
    const questionsTableBody = document.getElementById('questionsTableBody');
    const usersTableBody = document.getElementById('usersTableBody');
    const questionsTable = document.getElementById('questionsTable');
    const usersTable = document.getElementById('usersTable');
    const loadingState = document.getElementById('loadingState');
    const emptyState = document.getElementById('emptyState');
    const questionModal = document.getElementById('questionModal');
    const closeModal = document.getElementById('closeModal');
    const modalContent = document.getElementById('modalContent');
    const userQuestionsModal = document.getElementById('userQuestionsModal');
    const closeUserModal = document.getElementById('closeUserModal');
    const userQuestionsContent = document.getElementById('userQuestionsContent');
    const userModalTitle = document.getElementById('userModalTitle');
    const userModalSubtitle = document.getElementById('userModalSubtitle');

    // Stats elements
    const totalQuestions = document.getElementById('totalQuestions');
    const pendingQuestions = document.getElementById('pendingQuestions');
    const answeredQuestions = document.getElementById('answeredQuestions');
    const uniqueUsers = document.getElementById('uniqueUsers');

    // Bulk actions elements
    const selectAllCheckbox = document.getElementById('selectAllCheckbox');
    const bulkActionsBar = document.getElementById('bulkActionsBar');
    const selectedCount = document.getElementById('selectedCount');
    const bulkDeleteBtn = document.getElementById('bulkDeleteBtn');
    const bulkStatusBtn = document.getElementById('bulkStatusBtn');
    const clearSelectionBtn = document.getElementById('clearSelectionBtn');

    let allQuestions = [];
    let filteredQuestions = [];
    let allUsers = [];
    let currentViewMode = 'questions';
    let selectedQuestions = new Set(); // Pour la sélection multiple

    // Initialize dashboard
    init();

    async function init() {
        await loadQuestions();
        setupEventListeners();
    }

    // Event listeners
    function setupEventListeners() {
        if (logoutBtn) logoutBtn.addEventListener('click', handleLogout);
        if (refreshBtn) refreshBtn.addEventListener('click', handleRefresh);
        if (exportBtn) exportBtn.addEventListener('click', handleExport);
        if (searchInput) searchInput.addEventListener('input', handleSearch);
        if (statusFilter) statusFilter.addEventListener('change', handleFilter);
        if (userFilter) userFilter.addEventListener('change', handleFilter);
        if (viewMode) viewMode.addEventListener('change', handleViewModeChange);
        if (clearFiltersBtn) clearFiltersBtn.addEventListener('click', clearFilters);
        if (closeModal) closeModal.addEventListener('click', closeQuestionModal);
        if (closeUserModal) closeUserModal.addEventListener('click', closeUserQuestionsModal);

        // Bulk actions event listeners
        if (selectAllCheckbox) selectAllCheckbox.addEventListener('change', handleSelectAll);
        if (bulkDeleteBtn) bulkDeleteBtn.addEventListener('click', handleBulkDelete);
        if (bulkStatusBtn) bulkStatusBtn.addEventListener('click', handleBulkStatusChange);
        if (clearSelectionBtn) clearSelectionBtn.addEventListener('click', clearSelection);

        // Close modal on outside click
        if (questionModal) {
            questionModal.addEventListener('click', function(e) {
                if (e.target === questionModal) {
                    closeQuestionModal();
                }
            });
        }

        if (userQuestionsModal) {
            userQuestionsModal.addEventListener('click', function(e) {
                if (e.target === userQuestionsModal) {
                    closeUserQuestionsModal();
                }
            });
        }

        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeQuestionModal();
                closeUserQuestionsModal();
            }
            if (e.ctrlKey && e.key === 'r') {
                e.preventDefault();
                handleRefresh();
            }
        });
    }

    // Load questions from server
    async function loadQuestions() {
        try {
            showLoading();
            
            const response = await fetch('/api/admin/questions');
            const result = await response.json();

            if (result.success) {
                allQuestions = result.questions;
                filteredQuestions = [...allQuestions];
                generateUsersData();
                updateStats();
                updateUserFilter();
                renderCurrentView();
            } else {
                throw new Error(result.message || 'Failed to load questions');
            }
        } catch (error) {
            console.error('Error loading questions:', error);
            showError('Échec du chargement des questions: ' + error.message);
        } finally {
            hideLoading();
        }
    }

    // Generate users data from questions
    function generateUsersData() {
        const usersMap = new Map();

        allQuestions.forEach(question => {
            const email = question.user_email;
            if (!usersMap.has(email)) {
                usersMap.set(email, {
                    name: question.user_name,
                    email: question.user_email,
                    company: question.user_company,
                    phone: question.user_phone,
                    questions: [],
                    lastQuestionDate: question.created_at
                });
            }
            usersMap.get(email).questions.push(question);

            // Update last question date if this one is more recent
            if (new Date(question.created_at) > new Date(usersMap.get(email).lastQuestionDate)) {
                usersMap.get(email).lastQuestionDate = question.created_at;
            }
        });

        allUsers = Array.from(usersMap.values());
    }

    // Update statistics
    function updateStats() {
        const total = allQuestions.length;
        const pending = allQuestions.filter(q => q.status === 'pending').length;
        const answered = allQuestions.filter(q => q.status === 'answered').length;
        const users = new Set(allQuestions.map(q => q.user_email)).size;

        if (totalQuestions) totalQuestions.textContent = total;
        if (pendingQuestions) pendingQuestions.textContent = pending;
        if (answeredQuestions) answeredQuestions.textContent = answered;
        if (uniqueUsers) uniqueUsers.textContent = users;
    }

    // Update user filter dropdown
    function updateUserFilter() {
        if (!userFilter) return;

        // Clear existing options except the first one
        userFilter.innerHTML = '<option value="">Tous les Utilisateurs</option>';

        // Add user options
        allUsers.forEach(user => {
            const option = document.createElement('option');
            option.value = user.email;
            option.textContent = `${user.name} (${user.questions.length} questions)`;
            userFilter.appendChild(option);
        });
    }

    // Handle view mode change
    function handleViewModeChange() {
        currentViewMode = viewMode.value;
        renderCurrentView();
    }

    // Render current view based on mode
    function renderCurrentView() {
        if (currentViewMode === 'users') {
            renderUsers();
            if (questionsTable) questionsTable.classList.add('hidden');
            if (usersTable) usersTable.classList.remove('hidden');
        } else {
            renderQuestions();
            if (usersTable) usersTable.classList.add('hidden');
            if (questionsTable) questionsTable.classList.remove('hidden');
        }
    }

    // Render questions table
    function renderQuestions() {
        if (!questionsTableBody) return;

        if (filteredQuestions.length === 0) {
            questionsTableBody.innerHTML = '';
            if (emptyState) emptyState.classList.remove('hidden');
            return;
        }

        if (emptyState) emptyState.classList.add('hidden');

        questionsTableBody.innerHTML = filteredQuestions.map(question => `
            <tr class="hover:bg-gray-50 transition-colors ${selectedQuestions.has(question.id) ? 'bg-blue-50' : ''}">
                <td class="px-6 py-4 whitespace-nowrap">
                    <input type="checkbox" class="question-checkbox w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2"
                           data-question-id="${question.id}" ${selectedQuestions.has(question.id) ? 'checked' : ''}>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center">
                        <div class="flex-shrink-0 h-10 w-10">
                            <div class="h-10 w-10 rounded-full bg-gradient-to-r from-blue-400 to-purple-500 flex items-center justify-center">
                                <span class="text-white font-semibold text-sm">${question.user_name.charAt(0).toUpperCase()}</span>
                            </div>
                        </div>
                        <div class="ml-4">
                            <div class="text-sm font-medium text-gray-900">${escapeHtml(question.user_name)}</div>
                            <div class="text-sm text-gray-500">${escapeHtml(question.user_email)}</div>
                            ${question.user_company ? `<div class="text-xs text-gray-400">${escapeHtml(question.user_company)}</div>` : ''}
                        </div>
                    </div>
                </td>
                <td class="px-6 py-4">
                    <div class="text-sm text-gray-900 max-w-xs truncate" title="${escapeHtml(question.question_text)}">
                        ${escapeHtml(question.question_text)}
                    </div>
                    <button class="view-question-btn text-blue-600 hover:text-blue-800 text-xs mt-1" data-question-id="${question.id}">
                        Voir la question complète →
                    </button>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                    <select class="status-select status-badge status-${question.status} border-0 bg-transparent cursor-pointer" data-question-id="${question.id}">
                        <option value="pending" ${question.status === 'pending' ? 'selected' : ''}>En Attente</option>
                        <option value="reviewed" ${question.status === 'reviewed' ? 'selected' : ''}>Examinée</option>
                        <option value="answered" ${question.status === 'answered' ? 'selected' : ''}>Répondue</option>
                        <option value="archived" ${question.status === 'archived' ? 'selected' : ''}>Archivée</option>
                    </select>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    ${formatDate(question.created_at)}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <button class="view-question-action text-blue-600 hover:text-blue-900 mr-3" title="Voir" data-question-id="${question.id}">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="delete-question-action text-red-600 hover:text-red-900" title="Supprimer" data-question-id="${question.id}">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            </tr>
        `).join('');

        // Add event listeners to the newly created buttons
        addTableEventListeners();
    }

    // Render users table
    function renderUsers() {
        if (!usersTableBody) return;

        const filteredUsers = allUsers.filter(user => {
            const searchQuery = searchInput ? searchInput.value.toLowerCase() : '';
            const userFilterValue = userFilter ? userFilter.value : '';

            const matchesSearch = !searchQuery ||
                user.name.toLowerCase().includes(searchQuery) ||
                user.email.toLowerCase().includes(searchQuery) ||
                (user.company && user.company.toLowerCase().includes(searchQuery));

            const matchesUserFilter = !userFilterValue || user.email === userFilterValue;

            return matchesSearch && matchesUserFilter;
        });

        if (filteredUsers.length === 0) {
            usersTableBody.innerHTML = '';
            if (emptyState) emptyState.classList.remove('hidden');
            return;
        }

        if (emptyState) emptyState.classList.add('hidden');

        usersTableBody.innerHTML = filteredUsers.map(user => `
            <tr class="hover:bg-gray-50 transition-colors">
                <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center">
                        <div class="flex-shrink-0 h-10 w-10">
                            <div class="h-10 w-10 rounded-full bg-gradient-to-r from-blue-400 to-purple-500 flex items-center justify-center">
                                <span class="text-white font-semibold text-sm">${user.name.charAt(0).toUpperCase()}</span>
                            </div>
                        </div>
                        <div class="ml-4">
                            <div class="text-sm font-medium text-gray-900">${escapeHtml(user.name)}</div>
                        </div>
                    </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    ${escapeHtml(user.email)}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    ${user.company ? escapeHtml(user.company) : '-'}
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        ${user.questions.length} question${user.questions.length > 1 ? 's' : ''}
                    </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    ${formatDate(user.lastQuestionDate)}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <button class="view-user-questions text-blue-600 hover:text-blue-900 mr-3" title="Voir les questions" data-user-email="${user.email}">
                        <i class="fas fa-eye"></i> Voir Questions
                    </button>
                </td>
            </tr>
        `).join('');

        // Add event listeners to the newly created buttons
        addTableEventListeners();
    }

    // Search functionality
    function handleSearch() {
        applyFilters();
    }

    // Filter functionality
    function handleFilter() {
        applyFilters();
    }

    // Apply filters
    function applyFilters() {
        const searchQuery = searchInput ? searchInput.value.toLowerCase() : '';
        const statusValue = statusFilter ? statusFilter.value : '';
        const userFilterValue = userFilter ? userFilter.value : '';

        filteredQuestions = allQuestions.filter(question => {
            const matchesSearch = !searchQuery ||
                question.user_name.toLowerCase().includes(searchQuery) ||
                question.user_email.toLowerCase().includes(searchQuery) ||
                question.question_text.toLowerCase().includes(searchQuery) ||
                (question.user_company && question.user_company.toLowerCase().includes(searchQuery));

            const matchesStatus = !statusValue || question.status === statusValue;
            const matchesUser = !userFilterValue || question.user_email === userFilterValue;

            return matchesSearch && matchesStatus && matchesUser;
        });

        renderCurrentView();
    }

    // Clear filters
    function clearFilters() {
        if (searchInput) searchInput.value = '';
        if (statusFilter) statusFilter.value = '';
        if (userFilter) userFilter.value = '';
        filteredQuestions = [...allQuestions];
        renderCurrentView();
    }

    // Add event listeners to table buttons (called after rendering)
    function addTableEventListeners() {
        // View question buttons
        document.querySelectorAll('.view-question-btn, .view-question-action').forEach(btn => {
            btn.addEventListener('click', function() {
                const questionId = parseInt(this.dataset.questionId);
                viewQuestion(questionId);
            });
        });

        // Delete question buttons
        document.querySelectorAll('.delete-question-action').forEach(btn => {
            btn.addEventListener('click', function() {
                const questionId = parseInt(this.dataset.questionId);
                deleteQuestion(questionId);
            });
        });

        // Status select dropdowns
        document.querySelectorAll('.status-select').forEach(select => {
            select.addEventListener('change', function() {
                const questionId = parseInt(this.dataset.questionId);
                const newStatus = this.value;
                updateStatus(questionId, newStatus);
            });
        });

        // View user questions buttons
        document.querySelectorAll('.view-user-questions').forEach(btn => {
            btn.addEventListener('click', function() {
                const userEmail = this.dataset.userEmail;
                viewUserQuestions(userEmail);
            });
        });

        // Question checkboxes
        document.querySelectorAll('.question-checkbox').forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                const questionId = parseInt(this.dataset.questionId);
                if (this.checked) {
                    selectedQuestions.add(questionId);
                } else {
                    selectedQuestions.delete(questionId);
                }
                updateBulkActionsBar();
                updateSelectAllCheckbox();
            });
        });
    }

    // Handle logout
    async function handleLogout() {
        if (confirm('Êtes-vous sûr de vouloir vous déconnecter ?')) {
            try {
                await fetch('/api/admin/logout', { method: 'POST' });
                window.location.href = 'login.html';
            } catch (error) {
                console.error('Logout error:', error);
                window.location.href = 'login.html';
            }
        }
    }

    // Handle refresh
    async function handleRefresh() {
        refreshBtn.querySelector('i').classList.add('fa-spin');
        await loadQuestions();
        refreshBtn.querySelector('i').classList.remove('fa-spin');
    }

    // Handle export
    async function handleExport() {
        try {
            exportBtn.disabled = true;
            exportBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Exportation...';

            const response = await fetch('/api/admin/export');

            if (response.ok) {
                const blob = await response.blob();
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = 'winplus-faq-questions.xlsx';
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(url);
                document.body.removeChild(a);
            } else {
                throw new Error('Échec de l\'exportation');
            }
        } catch (error) {
            console.error('Export error:', error);
            alert('Échec de l\'exportation des questions');
        } finally {
            exportBtn.disabled = false;
            exportBtn.innerHTML = '<i class="fas fa-download"></i><span>Exporter Données</span>';
        }
    }

    // Add event listeners to question modal buttons
    function addQuestionModalEventListeners(questionId) {
        // Close button
        const closeBtn = document.querySelector('.modal-close-btn');
        if (closeBtn) {
            closeBtn.addEventListener('click', closeQuestionModal);
        }

        // Edit status button
        const editStatusBtn = document.querySelector('.modal-edit-status-btn');
        if (editStatusBtn) {
            editStatusBtn.addEventListener('click', function() {
                editQuestionStatus(questionId);
            });
        }
    }

    // Add event listeners to user modal buttons
    function addUserModalEventListeners(userEmail) {
        // Close button
        const closeBtn = document.querySelector('.user-modal-close-btn');
        if (closeBtn) {
            closeBtn.addEventListener('click', closeUserQuestionsModal);
        }

        // Export button
        const exportBtn = document.querySelector('.user-modal-export-btn');
        if (exportBtn) {
            exportBtn.addEventListener('click', function() {
                exportUserQuestions(userEmail);
            });
        }

        // View question buttons
        document.querySelectorAll('.user-modal-view-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const questionId = parseInt(this.dataset.questionId);
                closeUserQuestionsModal();
                viewQuestion(questionId);
            });
        });

        // Mark answered buttons
        document.querySelectorAll('.user-modal-mark-answered-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const questionId = parseInt(this.dataset.questionId);
                updateStatus(questionId, 'answered');
            });
        });
    }

    // Global functions for inline event handlers
    window.updateStatus = async function(questionId, newStatus) {
        try {
            const response = await fetch(`/api/admin/questions/${questionId}/status`, {
                method: 'PUT',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ status: newStatus })
            });

            const result = await response.json();
            if (result.success) {
                // Update local data
                const question = allQuestions.find(q => q.id === questionId);
                if (question) {
                    question.status = newStatus;
                    updateStats();
                    applyFilters(searchInput.value.toLowerCase(), statusFilter.value);
                }
            } else {
                throw new Error(result.message);
            }
        } catch (error) {
            console.error('Error updating status:', error);
            alert('Échec de la mise à jour du statut');
            await loadQuestions(); // Reload to reset
        }
    };

    window.deleteQuestion = async function(questionId) {
        if (confirm('Êtes-vous sûr de vouloir supprimer cette question ?')) {
            try {
                const response = await fetch(`/api/admin/questions/${questionId}`, {
                    method: 'DELETE'
                });

                const result = await response.json();
                if (result.success) {
                    await loadQuestions(); // Reload questions
                } else {
                    throw new Error(result.message);
                }
            } catch (error) {
                console.error('Error deleting question:', error);
                alert('Échec de la suppression de la question');
            }
        }
    };

    window.viewQuestion = function(questionId) {
        const question = allQuestions.find(q => q.id === questionId);
        if (question) {
            modalContent.innerHTML = `
                <div class="space-y-4">
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Nom</label>
                            <p class="mt-1 text-sm text-gray-900">${escapeHtml(question.user_name)}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Email</label>
                            <p class="mt-1 text-sm text-gray-900">${escapeHtml(question.user_email)}</p>
                        </div>
                    </div>
                    ${question.user_phone ? `
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Téléphone</label>
                            <p class="mt-1 text-sm text-gray-900">${escapeHtml(question.user_phone)}</p>
                        </div>
                    ` : ''}
                    ${question.user_company ? `
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Entreprise</label>
                            <p class="mt-1 text-sm text-gray-900">${escapeHtml(question.user_company)}</p>
                        </div>
                    ` : ''}
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Question</label>
                        <p class="mt-1 text-sm text-gray-900 bg-gray-50 p-3 rounded-lg">${escapeHtml(question.question_text)}</p>
                    </div>
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Statut</label>
                            <span class="mt-1 status-badge status-${question.status}">${getStatusText(question.status)}</span>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Soumise le</label>
                            <p class="mt-1 text-sm text-gray-900">${formatDate(question.created_at)}</p>
                        </div>
                    </div>
                </div>
            `;
            questionModal.classList.remove('hidden');
        }
    };

    function closeQuestionModal() {
        if (questionModal) questionModal.classList.add('hidden');
    }

    function closeUserQuestionsModal() {
        if (userQuestionsModal) userQuestionsModal.classList.add('hidden');
    }

    // Utility functions
    function showLoading() {
        loadingState.classList.remove('hidden');
        emptyState.classList.add('hidden');
    }

    function hideLoading() {
        loadingState.classList.add('hidden');
    }

    function showError(message) {
        alert(message); // Simple error handling - could be improved with toast notifications
    }

    function escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    function formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('fr-FR') + ' ' + date.toLocaleTimeString('fr-FR');
    }

    function getStatusText(status) {
        const statusMap = {
            'pending': 'En Attente',
            'reviewed': 'Examinée',
            'answered': 'Répondue',
            'archived': 'Archivée'
        };
        return statusMap[status] || status;
    }

    // Internal functions for actions
    function viewQuestion(questionId) {
        const question = allQuestions.find(q => q.id === questionId);
        if (question && modalContent) {
            modalContent.innerHTML = `
                <!-- User Info Card -->
                <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl p-6 mb-6">
                    <div class="flex items-center space-x-4 mb-4">
                        <div class="w-16 h-16 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-2xl flex items-center justify-center text-white text-2xl font-bold">
                            ${question.user_name.charAt(0).toUpperCase()}
                        </div>
                        <div>
                            <h4 class="text-xl font-bold text-gray-900">${escapeHtml(question.user_name)}</h4>
                            <p class="text-blue-600 font-medium">${escapeHtml(question.user_email)}</p>
                        </div>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        ${question.user_phone ? `
                            <div class="flex items-center space-x-2">
                                <i class="fas fa-phone text-blue-600"></i>
                                <span class="text-gray-700">${escapeHtml(question.user_phone)}</span>
                            </div>
                        ` : ''}
                        ${question.user_company ? `
                            <div class="flex items-center space-x-2">
                                <i class="fas fa-building text-blue-600"></i>
                                <span class="text-gray-700">${escapeHtml(question.user_company)}</span>
                            </div>
                        ` : ''}
                    </div>
                </div>

                <!-- Question Content -->
                <div class="bg-white border border-gray-200 rounded-2xl p-6 mb-6">
                    <h5 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                        <i class="fas fa-question-circle text-blue-600 mr-2"></i>
                        Question
                    </h5>
                    <div class="bg-gray-50 rounded-xl p-4 border-l-4 border-blue-500">
                        <p class="text-gray-800 leading-relaxed">${escapeHtml(question.question_text)}</p>
                    </div>
                </div>

                <!-- Status and Date Info -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="bg-white border border-gray-200 rounded-2xl p-6">
                        <h5 class="text-lg font-semibold text-gray-900 mb-3 flex items-center">
                            <i class="fas fa-flag text-green-600 mr-2"></i>
                            Statut
                        </h5>
                        <span class="status-badge status-${question.status} text-lg px-4 py-2">${getStatusText(question.status)}</span>
                    </div>
                    <div class="bg-white border border-gray-200 rounded-2xl p-6">
                        <h5 class="text-lg font-semibold text-gray-900 mb-3 flex items-center">
                            <i class="fas fa-calendar text-purple-600 mr-2"></i>
                            Informations
                        </h5>
                        <div class="space-y-2">
                            <p class="text-sm text-gray-600">Soumise le</p>
                            <p class="text-gray-900 font-medium">${formatDate(question.created_at)}</p>
                            ${question.updated_at !== question.created_at ? `
                                <p class="text-sm text-gray-600 mt-2">Mise à jour le</p>
                                <p class="text-gray-900 font-medium">${formatDate(question.updated_at)}</p>
                            ` : ''}
                        </div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="flex justify-end space-x-4 mt-8 pt-6 border-t border-gray-200">
                    <button class="modal-close-btn px-6 py-3 bg-gray-100 text-gray-700 rounded-xl hover:bg-gray-200 transition-colors">
                        <i class="fas fa-times mr-2"></i>Fermer
                    </button>
                    <button class="modal-edit-status-btn px-6 py-3 bg-blue-600 text-white rounded-xl hover:bg-blue-700 transition-colors" data-question-id="${question.id}">
                        <i class="fas fa-edit mr-2"></i>Modifier Statut
                    </button>
                </div>
            `;
            if (questionModal) {
                questionModal.classList.remove('hidden');
                // Add event listeners to modal buttons
                addQuestionModalEventListeners(question.id);
            }
        }
    }

    async function updateStatus(questionId, newStatus) {
        try {
            const response = await fetch(`/api/admin/questions/${questionId}/status`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ status: newStatus })
            });

            const result = await response.json();
            if (result.success) {
                await loadQuestions(); // Reload questions
            } else {
                throw new Error(result.message);
            }
        } catch (error) {
            console.error('Error updating status:', error);
            alert('Échec de la mise à jour du statut');
            await loadQuestions(); // Reload to reset
        }
    }

    async function deleteQuestion(questionId) {
        if (confirm('Êtes-vous sûr de vouloir supprimer cette question ?')) {
            try {
                const response = await fetch(`/api/admin/questions/${questionId}`, {
                    method: 'DELETE'
                });

                const result = await response.json();
                if (result.success) {
                    await loadQuestions(); // Reload questions
                } else {
                    throw new Error(result.message);
                }
            } catch (error) {
                console.error('Error deleting question:', error);
                alert('Échec de la suppression de la question');
            }
        }
    }

    function viewUserQuestions(userEmail) {
        const user = allUsers.find(u => u.email === userEmail);
        if (user && userQuestionsModal && userQuestionsContent) {
            // Update modal title
            if (userModalTitle) userModalTitle.textContent = `Questions de ${user.name}`;
            if (userModalSubtitle) userModalSubtitle.textContent = `${user.questions.length} question${user.questions.length > 1 ? 's' : ''} soumise${user.questions.length > 1 ? 's' : ''}`;

            // Generate questions content
            userQuestionsContent.innerHTML = `
                <!-- User Summary -->
                <div class="bg-gradient-to-r from-purple-50 to-pink-50 rounded-2xl p-6 mb-6">
                    <div class="flex items-center space-x-4 mb-4">
                        <div class="w-16 h-16 bg-gradient-to-r from-purple-600 to-pink-600 rounded-2xl flex items-center justify-center text-white text-2xl font-bold">
                            ${user.name.charAt(0).toUpperCase()}
                        </div>
                        <div>
                            <h4 class="text-xl font-bold text-gray-900">${escapeHtml(user.name)}</h4>
                            <p class="text-purple-600 font-medium">${escapeHtml(user.email)}</p>
                        </div>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        ${user.company ? `
                            <div class="flex items-center space-x-2">
                                <i class="fas fa-building text-purple-600"></i>
                                <span class="text-gray-700">${escapeHtml(user.company)}</span>
                            </div>
                        ` : ''}
                        <div class="flex items-center space-x-2">
                            <i class="fas fa-question-circle text-purple-600"></i>
                            <span class="text-gray-700">${user.questions.length} question${user.questions.length > 1 ? 's' : ''}</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <i class="fas fa-calendar text-purple-600"></i>
                            <span class="text-gray-700">Dernière: ${formatDate(user.lastQuestionDate)}</span>
                        </div>
                    </div>
                </div>

                <!-- Questions List -->
                <div class="space-y-4">
                    ${user.questions.map((question, index) => `
                        <div class="bg-white border border-gray-200 rounded-2xl p-6 hover:shadow-lg transition-shadow">
                            <div class="flex items-start justify-between mb-4">
                                <div class="flex items-center space-x-3">
                                    <div class="w-8 h-8 bg-gradient-to-r from-purple-600 to-pink-600 rounded-full flex items-center justify-center text-white text-sm font-bold">
                                        ${index + 1}
                                    </div>
                                    <div>
                                        <h5 class="font-semibold text-gray-900">Question #${question.id}</h5>
                                        <p class="text-sm text-gray-500">${formatDate(question.created_at)}</p>
                                    </div>
                                </div>
                                <span class="status-badge status-${question.status}">${getStatusText(question.status)}</span>
                            </div>
                            <div class="bg-gray-50 rounded-xl p-4 border-l-4 border-purple-500 mb-4">
                                <p class="text-gray-800 leading-relaxed">${escapeHtml(question.question_text)}</p>
                            </div>
                            <div class="flex justify-end space-x-2">
                                <button class="user-modal-view-btn px-4 py-2 bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 transition-colors text-sm" data-question-id="${question.id}">
                                    <i class="fas fa-eye mr-1"></i>Voir Détails
                                </button>
                                <button class="user-modal-mark-answered-btn px-4 py-2 bg-green-100 text-green-700 rounded-lg hover:bg-green-200 transition-colors text-sm" data-question-id="${question.id}">
                                    <i class="fas fa-check mr-1"></i>Marquer Répondue
                                </button>
                            </div>
                        </div>
                    `).join('')}
                </div>

                <!-- Action Buttons -->
                <div class="flex justify-between items-center mt-8 pt-6 border-t border-gray-200">
                    <div class="text-sm text-gray-500">
                        Total: ${user.questions.length} question${user.questions.length > 1 ? 's' : ''}
                    </div>
                    <div class="flex space-x-4">
                        <button class="user-modal-close-btn px-6 py-3 bg-gray-100 text-gray-700 rounded-xl hover:bg-gray-200 transition-colors">
                            <i class="fas fa-times mr-2"></i>Fermer
                        </button>
                        <button class="user-modal-export-btn px-6 py-3 bg-purple-600 text-white rounded-xl hover:bg-purple-700 transition-colors" data-user-email="${userEmail}">
                            <i class="fas fa-download mr-2"></i>Exporter Questions
                        </button>
                    </div>
                </div>
            `;

            // Show modal
            userQuestionsModal.classList.remove('hidden');
            // Add event listeners to modal buttons
            addUserModalEventListeners(userEmail);
        }
    }

    // Global function to set view mode (for other pages)
    window.setViewMode = function(mode) {
        if (mode === 'users') {
            currentViewMode = 'users';
            if (viewMode) viewMode.value = 'users';
            renderCurrentView();
        } else {
            currentViewMode = 'questions';
            if (viewMode) viewMode.value = 'questions';
            renderCurrentView();
        }
    };

    // Global functions for modal actions
    window.closeQuestionModal = closeQuestionModal;
    window.closeUserQuestionsModal = closeUserQuestionsModal;

    // Function to edit question status from modal
    window.editQuestionStatus = function(questionId) {
        const question = allQuestions.find(q => q.id === questionId);
        if (question) {
            const newStatus = prompt('Nouveau statut (pending/reviewed/answered/archived):', question.status);
            if (newStatus && ['pending', 'reviewed', 'answered', 'archived'].includes(newStatus)) {
                updateStatus(questionId, newStatus);
                closeQuestionModal();
            }
        }
    };

    // Function to export user questions
    window.exportUserQuestions = function(userEmail) {
        const user = allUsers.find(u => u.email === userEmail);
        if (user) {
            // Create CSV content
            const csvContent = [
                ['ID', 'Question', 'Statut', 'Date de Création', 'Date de Mise à Jour'],
                ...user.questions.map(q => [
                    q.id,
                    `"${q.question_text.replace(/"/g, '""')}"`,
                    getStatusText(q.status),
                    formatDate(q.created_at),
                    formatDate(q.updated_at)
                ])
            ].map(row => row.join(',')).join('\n');

            // Download CSV
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', `questions-${user.name.replace(/\s+/g, '-')}.csv`);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }
    };

    // Bulk actions functions
    function handleSelectAll() {
        const isChecked = selectAllCheckbox.checked;

        if (isChecked) {
            // Sélectionner toutes les questions visibles
            filteredQuestions.forEach(question => {
                selectedQuestions.add(question.id);
            });
        } else {
            // Désélectionner toutes les questions
            selectedQuestions.clear();
        }

        updateBulkActionsBar();
        renderCurrentView(); // Re-render pour mettre à jour les checkboxes
    }

    function updateSelectAllCheckbox() {
        if (!selectAllCheckbox) return;

        const visibleQuestionIds = filteredQuestions.map(q => q.id);
        const selectedVisibleQuestions = visibleQuestionIds.filter(id => selectedQuestions.has(id));

        if (selectedVisibleQuestions.length === 0) {
            selectAllCheckbox.checked = false;
            selectAllCheckbox.indeterminate = false;
        } else if (selectedVisibleQuestions.length === visibleQuestionIds.length) {
            selectAllCheckbox.checked = true;
            selectAllCheckbox.indeterminate = false;
        } else {
            selectAllCheckbox.checked = false;
            selectAllCheckbox.indeterminate = true;
        }
    }

    function updateBulkActionsBar() {
        if (!bulkActionsBar || !selectedCount) return;

        const count = selectedQuestions.size;

        if (count > 0) {
            bulkActionsBar.classList.remove('hidden');
            selectedCount.textContent = `${count} sélectionnée${count > 1 ? 's' : ''}`;
        } else {
            bulkActionsBar.classList.add('hidden');
        }
    }

    function clearSelection() {
        selectedQuestions.clear();
        updateBulkActionsBar();
        updateSelectAllCheckbox();
        renderCurrentView();
    }

    async function handleBulkDelete() {
        if (selectedQuestions.size === 0) return;

        const count = selectedQuestions.size;

        // Utiliser SweetAlert pour une belle confirmation
        const result = await Swal.fire({
            title: 'Confirmer la suppression',
            text: `Êtes-vous sûr de vouloir supprimer ${count} question${count > 1 ? 's' : ''} ?`,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: `Oui, supprimer ${count > 1 ? 'les' : 'la'} question${count > 1 ? 's' : ''}`,
            cancelButtonText: 'Annuler',
            confirmButtonColor: '#ef4444',
            cancelButtonColor: '#6b7280',
            reverseButtons: true,
            showClass: {
                popup: 'animate__animated animate__fadeInDown'
            },
            hideClass: {
                popup: 'animate__animated animate__fadeOutUp'
            }
        });

        if (!result.isConfirmed) return;

        try {
            bulkDeleteBtn.disabled = true;
            bulkDeleteBtn.innerHTML = '<i class="fas fa-spinner fa-spin text-sm"></i><span>Suppression...</span>';

            const questionIds = Array.from(selectedQuestions);
            const response = await fetch('/api/admin/questions/bulk-delete', {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ questionIds })
            });

            const result = await response.json();

            if (result.success) {
                // Message de succès avec SweetAlert
                await Swal.fire({
                    title: 'Suppression réussie !',
                    text: `${result.deletedCount} question${result.deletedCount > 1 ? 's' : ''} supprimée${result.deletedCount > 1 ? 's' : ''} avec succès`,
                    icon: 'success',
                    confirmButtonText: 'Parfait !',
                    confirmButtonColor: '#10b981',
                    timer: 3000,
                    timerProgressBar: true,
                    showClass: {
                        popup: 'animate__animated animate__fadeInDown'
                    },
                    hideClass: {
                        popup: 'animate__animated animate__fadeOutUp'
                    }
                });
                clearSelection();
                await loadQuestions(); // Recharger les données
            } else {
                throw new Error(result.message || 'Échec de la suppression');
            }
        } catch (error) {
            console.error('Bulk delete error:', error);
            // Message d'erreur avec SweetAlert
            await Swal.fire({
                title: 'Erreur de suppression',
                text: 'Erreur lors de la suppression: ' + error.message,
                icon: 'error',
                confirmButtonText: 'OK',
                confirmButtonColor: '#ef4444',
                showClass: {
                    popup: 'animate__animated animate__shakeX'
                },
                hideClass: {
                    popup: 'animate__animated animate__fadeOutUp'
                }
            });
        } finally {
            bulkDeleteBtn.disabled = false;
            bulkDeleteBtn.innerHTML = '<i class="fas fa-trash text-sm"></i><span>Supprimer</span>';
        }
    }

    async function handleBulkStatusChange() {
        if (selectedQuestions.size === 0) return;

        // Utiliser SweetAlert pour une belle interface de sélection
        const { value: newStatus } = await Swal.fire({
            title: 'Changer le statut',
            text: `Sélectionnez le nouveau statut pour ${selectedQuestions.size} question${selectedQuestions.size > 1 ? 's' : ''}`,
            input: 'select',
            inputOptions: {
                'pending': '⏳ En attente',
                'reviewed': '👀 Examiné',
                'answered': '✅ Répondu',
                'archived': '📁 Archivé'
            },
            inputPlaceholder: 'Choisissez un statut',
            showCancelButton: true,
            confirmButtonText: 'Changer le statut',
            cancelButtonText: 'Annuler',
            confirmButtonColor: '#1a73e8',
            cancelButtonColor: '#6b7280',
            inputValidator: (value) => {
                if (!value) {
                    return 'Vous devez sélectionner un statut !';
                }
            },
            showClass: {
                popup: 'animate__animated animate__fadeInDown'
            },
            hideClass: {
                popup: 'animate__animated animate__fadeOutUp'
            }
        });

        if (!newStatus) return; // Utilisateur a annulé

        try {
            bulkStatusBtn.disabled = true;
            bulkStatusBtn.innerHTML = '<i class="fas fa-spinner fa-spin text-sm"></i><span>Mise à jour...</span>';

            const questionIds = Array.from(selectedQuestions);
            const response = await fetch('/api/admin/questions/bulk-status', {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ questionIds, status: newStatus })
            });

            const result = await response.json();

            if (result.success) {
                // Message de succès avec SweetAlert
                const statusLabels = {
                    'pending': '⏳ En attente',
                    'reviewed': '👀 Examiné',
                    'answered': '✅ Répondu',
                    'archived': '📁 Archivé'
                };

                await Swal.fire({
                    title: 'Statut mis à jour !',
                    text: `${result.updatedCount} question${result.updatedCount > 1 ? 's' : ''} mise${result.updatedCount > 1 ? 's' : ''} à jour vers "${statusLabels[newStatus]}" avec succès`,
                    icon: 'success',
                    confirmButtonText: 'Parfait !',
                    confirmButtonColor: '#10b981',
                    timer: 3000,
                    timerProgressBar: true,
                    showClass: {
                        popup: 'animate__animated animate__fadeInDown'
                    },
                    hideClass: {
                        popup: 'animate__animated animate__fadeOutUp'
                    }
                });
                clearSelection();
                await loadQuestions(); // Recharger les données
            } else {
                throw new Error(result.message || 'Échec de la mise à jour');
            }
        } catch (error) {
            console.error('Bulk status update error:', error);
            // Message d'erreur avec SweetAlert
            await Swal.fire({
                title: 'Erreur de mise à jour',
                text: 'Erreur lors de la mise à jour du statut: ' + error.message,
                icon: 'error',
                confirmButtonText: 'OK',
                confirmButtonColor: '#ef4444',
                showClass: {
                    popup: 'animate__animated animate__shakeX'
                },
                hideClass: {
                    popup: 'animate__animated animate__fadeOutUp'
                }
            });
        } finally {
            bulkStatusBtn.disabled = false;
            bulkStatusBtn.innerHTML = '<i class="fas fa-edit text-sm"></i><span>Changer Statut</span>';
        }
    }
});
