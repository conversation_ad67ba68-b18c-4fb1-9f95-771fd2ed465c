<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Winplus FAQ - Paramètres</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <link href="css/styles.css" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'inter': ['Inter', 'sans-serif'],
                    },
                    colors: {
                        primary: {
                            50: '#f0f9ff',
                            100: '#e0f2fe',
                            500: '#0ea5e9',
                            600: '#0284c7',
                            700: '#0369a1',
                            900: '#0c4a6e'
                        },
                        dark: {
                            50: '#f8fafc',
                            100: '#f1f5f9',
                            800: '#1e293b',
                            900: '#0f172a'
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 min-h-screen font-inter">
    <!-- Modern Sidebar -->
    <div class="flex">
        <aside class="fixed left-0 top-0 h-full w-72 bg-white/80 backdrop-blur-xl border-r border-gray-200/50 shadow-2xl z-40">
            <div class="p-8">
                <!-- Logo -->
                <div class="flex items-center space-x-4 mb-12">
                    <div class="w-12 h-12 bg-gradient-to-tr from-blue-600 to-indigo-600 rounded-2xl flex items-center justify-center shadow-lg">
                        <i class="fas fa-pills text-white text-xl"></i>
                    </div>
                    <div>
                        <h1 class="text-xl font-bold text-gray-900">Winplus</h1>
                        <p class="text-sm text-gray-500">Admin FAQ</p>
                    </div>
                </div>

                <!-- Navigation -->
                <nav class="space-y-2">
                    <a href="admin.html" class="flex items-center space-x-3 px-4 py-3 rounded-xl text-gray-600 hover:bg-gray-50 transition-colors">
                        <i class="fas fa-chart-bar w-5"></i>
                        <span>Tableau de Bord</span>
                    </a>
                    <a href="questions.html" class="flex items-center space-x-3 px-4 py-3 rounded-xl text-gray-600 hover:bg-gray-50 transition-colors">
                        <i class="fas fa-question-circle w-5"></i>
                        <span>Questions</span>
                    </a>
                    <a href="users.html" class="flex items-center space-x-3 px-4 py-3 rounded-xl text-gray-600 hover:bg-gray-50 transition-colors">
                        <i class="fas fa-users w-5"></i>
                        <span>Utilisateurs</span>
                    </a>
                    <a href="settings.html" class="flex items-center space-x-3 px-4 py-3 rounded-xl bg-blue-50 text-blue-700 font-medium">
                        <i class="fas fa-cog w-5"></i>
                        <span>Paramètres</span>
                    </a>
                </nav>

                <!-- Actions -->
                <div class="mt-12 space-y-3">
                    <button id="exportBtn" class="w-full flex items-center justify-center space-x-2 px-4 py-3 bg-gradient-to-r from-emerald-500 to-teal-600 text-white rounded-xl font-medium hover:shadow-lg transition-all">
                        <i class="fas fa-download"></i>
                        <span>Exporter Données</span>
                    </button>
                    <button id="refreshBtn" class="w-full flex items-center justify-center space-x-2 px-4 py-3 bg-gray-100 text-gray-700 rounded-xl font-medium hover:bg-gray-200 transition-colors">
                        <i class="fas fa-sync-alt"></i>
                        <span>Actualiser</span>
                    </button>
                </div>

                <!-- Logout -->
                <div class="absolute bottom-8 left-8 right-8">
                    <button id="logoutBtn" class="w-full flex items-center justify-center space-x-2 px-4 py-3 bg-red-50 text-red-600 rounded-xl font-medium hover:bg-red-100 transition-colors">
                        <i class="fas fa-sign-out-alt"></i>
                        <span>Déconnexion</span>
                    </button>
                </div>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="flex-1 ml-72">
            <!-- Header -->
            <header class="bg-white/70 backdrop-blur-xl border-b border-gray-200/50 p-8">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-3xl font-bold text-gray-900">Paramètres</h1>
                        <p class="text-gray-600 mt-1">Configuration et paramètres de l'application FAQ</p>
                    </div>
                    <div class="flex items-center space-x-4">
                        <div class="w-10 h-10 bg-gradient-to-tr from-blue-600 to-indigo-600 rounded-full flex items-center justify-center">
                            <i class="fas fa-user text-white text-sm"></i>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Settings Content -->
            <div class="p-8 space-y-8">
                <!-- General Settings -->
                <div class="bg-white/70 backdrop-blur-xl rounded-2xl border border-gray-200/50 shadow-lg overflow-hidden">
                    <div class="p-6 border-b border-gray-200">
                        <h2 class="text-xl font-bold text-gray-900">Paramètres Généraux</h2>
                        <p class="text-gray-600 mt-1">Configuration de base de l'application</p>
                    </div>
                    <div class="p-6 space-y-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Nom de l'Application</label>
                                <input type="text" value="Winplus FAQ" class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Email de Contact</label>
                                <input type="email" value="<EMAIL>" class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            </div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Description</label>
                            <textarea rows="3" class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent">Système FAQ pour la plateforme de gestion pharmaceutique Winplus</textarea>
                        </div>
                    </div>
                </div>

                <!-- Database Settings -->
                <div class="bg-white/70 backdrop-blur-xl rounded-2xl border border-gray-200/50 shadow-lg overflow-hidden">
                    <div class="p-6 border-b border-gray-200">
                        <h2 class="text-xl font-bold text-gray-900">Base de Données</h2>
                        <p class="text-gray-600 mt-1">Informations et maintenance de la base de données</p>
                    </div>
                    <div class="p-6 space-y-6">
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                            <div class="bg-gray-50 p-4 rounded-xl">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <p class="text-sm font-medium text-gray-600">Type de Base</p>
                                        <p class="text-lg font-bold text-gray-900">SQLite</p>
                                    </div>
                                    <i class="fas fa-database text-blue-600 text-xl"></i>
                                </div>
                            </div>
                            <div class="bg-gray-50 p-4 rounded-xl">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <p class="text-sm font-medium text-gray-600">Taille</p>
                                        <p class="text-lg font-bold text-gray-900" id="dbSize">Calcul...</p>
                                    </div>
                                    <i class="fas fa-hdd text-green-600 text-xl"></i>
                                </div>
                            </div>
                            <div class="bg-gray-50 p-4 rounded-xl">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <p class="text-sm font-medium text-gray-600">Dernière Sauvegarde</p>
                                        <p class="text-lg font-bold text-gray-900">Automatique</p>
                                    </div>
                                    <i class="fas fa-shield-alt text-purple-600 text-xl"></i>
                                </div>
                            </div>
                        </div>
                        <div class="flex space-x-4">
                            <button id="backupBtn" class="px-6 py-3 bg-blue-600 text-white rounded-xl hover:bg-blue-700 transition-colors">
                                <i class="fas fa-download mr-2"></i>Sauvegarder DB
                            </button>
                            <button id="clearCacheBtn" class="px-6 py-3 bg-gray-100 text-gray-700 rounded-xl hover:bg-gray-200 transition-colors">
                                <i class="fas fa-broom mr-2"></i>Nettoyer Cache
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Security Settings -->
                <div class="bg-white/70 backdrop-blur-xl rounded-2xl border border-gray-200/50 shadow-lg overflow-hidden">
                    <div class="p-6 border-b border-gray-200">
                        <h2 class="text-xl font-bold text-gray-900">Sécurité</h2>
                        <p class="text-gray-600 mt-1">Paramètres de sécurité et d'accès</p>
                    </div>
                    <div class="p-6 space-y-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Durée de Session (minutes)</label>
                                <input type="number" value="1440" class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Limite de Requêtes/Minute</label>
                                <input type="number" value="100" class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            </div>
                        </div>
                        <div class="space-y-4">
                            <div class="flex items-center justify-between p-4 bg-gray-50 rounded-xl">
                                <div>
                                    <h3 class="font-medium text-gray-900">Authentification Requise</h3>
                                    <p class="text-sm text-gray-600">Exiger une connexion pour accéder au dashboard</p>
                                </div>
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" checked class="sr-only peer">
                                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                                </label>
                            </div>
                            <div class="flex items-center justify-between p-4 bg-gray-50 rounded-xl">
                                <div>
                                    <h3 class="font-medium text-gray-900">Logs d'Activité</h3>
                                    <p class="text-sm text-gray-600">Enregistrer toutes les actions administrateur</p>
                                </div>
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" checked class="sr-only peer">
                                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Export Settings -->
                <div class="bg-white/70 backdrop-blur-xl rounded-2xl border border-gray-200/50 shadow-lg overflow-hidden">
                    <div class="p-6 border-b border-gray-200">
                        <h2 class="text-xl font-bold text-gray-900">Export et Sauvegarde</h2>
                        <p class="text-gray-600 mt-1">Configuration des exports et sauvegardes automatiques</p>
                    </div>
                    <div class="p-6 space-y-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Format d'Export par Défaut</label>
                                <select class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                    <option value="xlsx">Excel (.xlsx)</option>
                                    <option value="csv">CSV (.csv)</option>
                                    <option value="json">JSON (.json)</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Sauvegarde Automatique</label>
                                <select class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                    <option value="daily">Quotidienne</option>
                                    <option value="weekly">Hebdomadaire</option>
                                    <option value="monthly">Mensuelle</option>
                                    <option value="disabled">Désactivée</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- System Info -->
                <div class="bg-white/70 backdrop-blur-xl rounded-2xl border border-gray-200/50 shadow-lg overflow-hidden">
                    <div class="p-6 border-b border-gray-200">
                        <h2 class="text-xl font-bold text-gray-900">Informations Système</h2>
                        <p class="text-gray-600 mt-1">Détails techniques de l'application</p>
                    </div>
                    <div class="p-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                            <div class="bg-gray-50 p-4 rounded-xl text-center">
                                <p class="text-sm font-medium text-gray-600">Version</p>
                                <p class="text-lg font-bold text-gray-900">1.0.0</p>
                            </div>
                            <div class="bg-gray-50 p-4 rounded-xl text-center">
                                <p class="text-sm font-medium text-gray-600">Node.js</p>
                                <p class="text-lg font-bold text-gray-900" id="nodeVersion">-</p>
                            </div>
                            <div class="bg-gray-50 p-4 rounded-xl text-center">
                                <p class="text-sm font-medium text-gray-600">Uptime</p>
                                <p class="text-lg font-bold text-gray-900" id="uptime">-</p>
                            </div>
                            <div class="bg-gray-50 p-4 rounded-xl text-center">
                                <p class="text-sm font-medium text-gray-600">Mémoire</p>
                                <p class="text-lg font-bold text-gray-900" id="memory">-</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Save Button -->
                <div class="flex justify-end">
                    <button id="saveSettingsBtn" class="px-8 py-3 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-xl font-medium hover:shadow-lg hover:scale-105 transition-all">
                        <i class="fas fa-save mr-2"></i>Sauvegarder les Paramètres
                    </button>
                </div>
            </div>
        </main>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Load real system info
            loadSystemInfo();

            // Get base URL for API calls
            function getApiUrl(endpoint) {
                // Force HTTP protocol and use the current host
                const host = window.location.hostname;
                const port = '3000';
                return `http://${host}:${port}${endpoint}`;
            }

            // Add functionality for all buttons
            document.getElementById('logoutBtn')?.addEventListener('click', function() {
                if (confirm('Êtes-vous sûr de vouloir vous déconnecter ?')) {
                    window.location.href = 'login.html';
                }
            });

            document.getElementById('exportBtn')?.addEventListener('click', function() {
                window.location.href = '/api/admin/export';
            });

            document.getElementById('refreshBtn')?.addEventListener('click', function() {
                location.reload();
            });

            // Settings specific buttons
            document.getElementById('backupBtn')?.addEventListener('click', async function() {
                this.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Sauvegarde...';
                this.disabled = true;

                try {
                    // Vérifier d'abord si on est connecté
                    await ensureLoggedIn();

                    const response = await fetch(getApiUrl('/api/admin/backup'), {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        credentials: 'include' // Important pour inclure les cookies de session
                    });

                    const result = await response.json();

                    if (result.success) {
                        this.innerHTML = '<i class="fas fa-check mr-2"></i>Sauvegardé !';
                        this.classList.remove('bg-blue-600', 'hover:bg-blue-700');
                        this.classList.add('bg-green-600');

                        // Show success notification
                        showNotification(`Sauvegarde créée: ${result.filename} (${formatBytes(result.size)})`, 'success');

                        setTimeout(() => {
                            this.innerHTML = '<i class="fas fa-download mr-2"></i>Sauvegarder DB';
                            this.classList.remove('bg-green-600');
                            this.classList.add('bg-blue-600', 'hover:bg-blue-700');
                            this.disabled = false;
                        }, 2000);
                    } else {
                        throw new Error(result.message);
                    }
                } catch (error) {
                    this.innerHTML = '<i class="fas fa-exclamation-triangle mr-2"></i>Erreur !';
                    this.classList.remove('bg-blue-600', 'hover:bg-blue-700');
                    this.classList.add('bg-red-600');

                    showNotification('Erreur lors de la sauvegarde: ' + error.message, 'error');

                    setTimeout(() => {
                        this.innerHTML = '<i class="fas fa-download mr-2"></i>Sauvegarder DB';
                        this.classList.remove('bg-red-600');
                        this.classList.add('bg-blue-600', 'hover:bg-blue-700');
                        this.disabled = false;
                    }, 3000);
                }
            });

            document.getElementById('clearCacheBtn')?.addEventListener('click', async function() {
                if (confirm('Êtes-vous sûr de vouloir nettoyer le cache ?')) {
                    this.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Nettoyage...';
                    this.disabled = true;

                    try {
                        // Vérifier d'abord si on est connecté
                        await ensureLoggedIn();

                        const response = await fetch(getApiUrl('/api/admin/clear-cache'), {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            credentials: 'include' // Important pour inclure les cookies de session
                        });

                        const result = await response.json();

                        if (result.success) {
                            this.innerHTML = '<i class="fas fa-check mr-2"></i>Nettoyé !';
                            this.classList.remove('bg-gray-100', 'text-gray-700');
                            this.classList.add('bg-green-100', 'text-green-700');

                            showNotification(`Cache nettoyé: ${result.clearedItems} éléments supprimés`, 'success');

                            setTimeout(() => {
                                this.innerHTML = '<i class="fas fa-broom mr-2"></i>Nettoyer Cache';
                                this.classList.remove('bg-green-100', 'text-green-700');
                                this.classList.add('bg-gray-100', 'text-gray-700');
                                this.disabled = false;
                            }, 2000);
                        } else {
                            throw new Error(result.message);
                        }
                    } catch (error) {
                        this.innerHTML = '<i class="fas fa-exclamation-triangle mr-2"></i>Erreur !';
                        this.classList.remove('bg-gray-100', 'text-gray-700');
                        this.classList.add('bg-red-100', 'text-red-700');

                        showNotification('Erreur lors du nettoyage: ' + error.message, 'error');

                        setTimeout(() => {
                            this.innerHTML = '<i class="fas fa-broom mr-2"></i>Nettoyer Cache';
                            this.classList.remove('bg-red-100', 'text-red-700');
                            this.classList.add('bg-gray-100', 'text-gray-700');
                            this.disabled = false;
                        }, 3000);
                    }
                }
            });

            document.getElementById('saveSettingsBtn')?.addEventListener('click', function() {
                this.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Sauvegarde...';
                this.disabled = true;

                // Simulate save process
                setTimeout(() => {
                    this.innerHTML = '<i class="fas fa-check mr-2"></i>Sauvegardé !';
                    this.classList.remove('from-blue-600', 'to-indigo-600');
                    this.classList.add('from-green-600', 'to-green-700');

                    setTimeout(() => {
                        this.innerHTML = '<i class="fas fa-save mr-2"></i>Sauvegarder les Paramètres';
                        this.classList.remove('from-green-600', 'to-green-700');
                        this.classList.add('from-blue-600', 'to-indigo-600');
                        this.disabled = false;
                    }, 2000);
                }, 1500);
            });

            // Toggle switches functionality
            document.querySelectorAll('input[type="checkbox"]').forEach(checkbox => {
                checkbox.addEventListener('change', function() {
                    const setting = this.closest('.flex').querySelector('h3').textContent;
                    const status = this.checked ? 'activé' : 'désactivé';

                    // Show temporary notification
                    const notification = document.createElement('div');
                    notification.className = 'fixed top-4 right-4 bg-blue-600 text-white px-4 py-2 rounded-lg shadow-lg z-50';
                    notification.textContent = `${setting} ${status}`;
                    document.body.appendChild(notification);

                    setTimeout(() => {
                        notification.remove();
                    }, 3000);
                });
            });

            // Ensure user is logged in
            async function ensureLoggedIn() {
                try {
                    // Test if we're already logged in by trying to access a protected endpoint
                    const testResponse = await fetch(getApiUrl('/api/admin/system-info'), {
                        credentials: 'include'
                    });

                    if (testResponse.status === 401) {
                        console.log('🔐 Non connecté, redirection vers login...');
                        showNotification('Session expirée, redirection vers la connexion...', 'error');
                        setTimeout(() => {
                            window.location.href = 'login.html';
                        }, 2000);
                        throw new Error('Non authentifié');
                    }
                } catch (error) {
                    if (error.message !== 'Non authentifié') {
                        console.log('🔐 Erreur vérification connexion:', error.message);
                        showNotification('Erreur de connexion, redirection...', 'error');
                        setTimeout(() => {
                            window.location.href = 'login.html';
                        }, 2000);
                        throw error;
                    }
                    throw error;
                }
            }

            // Load system info from API
            async function loadSystemInfo() {
                try {
                    const response = await fetch(getApiUrl('/api/admin/system-info'));
                    const result = await response.json();

                    if (result.success) {
                        const data = result.data;

                        document.getElementById('nodeVersion').textContent = data.nodeVersion;
                        document.getElementById('uptime').textContent = formatUptime(data.uptime);
                        document.getElementById('memory').textContent = formatBytes(data.memoryUsage.heapUsed);
                        document.getElementById('dbSize').textContent = formatBytes(data.dbSize);
                    }
                } catch (error) {
                    console.error('Error loading system info:', error);
                    // Fallback values
                    document.getElementById('nodeVersion').textContent = 'N/A';
                    document.getElementById('uptime').textContent = 'N/A';
                    document.getElementById('memory').textContent = 'N/A';
                    document.getElementById('dbSize').textContent = 'N/A';
                }
            }

            // Utility functions
            function formatBytes(bytes) {
                if (bytes === 0) return '0 B';
                const k = 1024;
                const sizes = ['B', 'KB', 'MB', 'GB'];
                const i = Math.floor(Math.log(bytes) / Math.log(k));
                return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
            }

            function formatUptime(seconds) {
                const hours = Math.floor(seconds / 3600);
                const minutes = Math.floor((seconds % 3600) / 60);
                return `${hours}h ${minutes}m`;
            }

            function showNotification(message, type = 'info') {
                const notification = document.createElement('div');
                notification.className = `fixed top-4 right-4 px-6 py-4 rounded-lg shadow-lg z-50 ${
                    type === 'success' ? 'bg-green-600 text-white' :
                    type === 'error' ? 'bg-red-600 text-white' :
                    'bg-blue-600 text-white'
                }`;
                notification.innerHTML = `
                    <div class="flex items-center space-x-2">
                        <i class="fas ${
                            type === 'success' ? 'fa-check-circle' :
                            type === 'error' ? 'fa-exclamation-triangle' :
                            'fa-info-circle'
                        }"></i>
                        <span>${message}</span>
                    </div>
                `;

                document.body.appendChild(notification);

                setTimeout(() => {
                    notification.remove();
                }, 5000);
            }
        });
    </script>
</body>
</html>
