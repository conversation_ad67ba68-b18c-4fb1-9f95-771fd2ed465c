const fs = require('fs');
const path = require('path');
const { spawn } = require('child_process');

console.log('🚀 Starting Winplus FAQ Application...');

// Ensure data directory exists
const dataDir = path.join(__dirname, '..', 'data');
if (!fs.existsSync(dataDir)) {
    fs.mkdirSync(dataDir, { recursive: true });
    console.log('📁 Created data directory');
}

const dbPath = path.join(dataDir, 'faq.db');
const dbExists = fs.existsSync(dbPath);

console.log(`📊 Database status: ${dbExists ? 'EXISTS' : 'NOT FOUND'}`);

function runScript(scriptPath) {
    return new Promise((resolve, reject) => {
        console.log(`🔄 Running: ${scriptPath}`);
        
        const child = spawn('node', [scriptPath], {
            stdio: 'inherit',
            cwd: path.join(__dirname, '..')
        });
        
        child.on('close', (code) => {
            if (code === 0) {
                console.log(`✅ ${scriptPath} completed successfully`);
                resolve();
            } else {
                console.error(`❌ ${scriptPath} failed with code ${code}`);
                reject(new Error(`Script failed with code ${code}`));
            }
        });
        
        child.on('error', (err) => {
            console.error(`❌ Error running ${scriptPath}:`, err);
            reject(err);
        });
    });
}

async function startup() {
    try {
        if (!dbExists) {
            // Database doesn't exist, run initialization
            console.log('🔧 Database not found, initializing...');
            await runScript('scripts/init-db.js');
        } else {
            // Database exists, run migration to ensure schema is up to date
            console.log('🔄 Database found, running migration...');
            await runScript('scripts/migrate-db.js');
        }
        
        console.log('🎉 Database setup completed successfully!');
        console.log('🚀 Starting server...');
        
        // Start the main server
        require('../server.js');
        
    } catch (error) {
        console.error('❌ Startup failed:', error);
        process.exit(1);
    }
}

startup();
