<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Winplus FAQ - Connexion Admin</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <link href="css/styles.css" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'inter': ['Inter', 'sans-serif'],
                    },
                    colors: {
                        primary: {
                            50: '#f0f9ff',
                            100: '#e0f2fe',
                            500: '#0ea5e9',
                            600: '#0284c7',
                            700: '#0369a1',
                            900: '#0c4a6e'
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 min-h-screen font-inter flex items-center justify-center p-6">
    <!-- Background Pattern -->
    <div class="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%23000000" fill-opacity="0.02"%3E%3Ccircle cx="30" cy="30" r="1"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-50"></div>
    
    <!-- Decorative Elements -->
    <div class="absolute top-20 left-20 w-32 h-32 bg-blue-200/30 rounded-full blur-3xl"></div>
    <div class="absolute bottom-20 right-20 w-40 h-40 bg-indigo-200/30 rounded-full blur-3xl"></div>
    <div class="absolute top-1/2 left-10 w-24 h-24 bg-purple-200/30 rounded-full blur-2xl"></div>

    <div class="relative z-10 w-full max-w-md">
        <!-- Login Card -->
        <div class="bg-white/70 backdrop-blur-xl rounded-3xl p-8 border border-gray-200/50 shadow-2xl">
            <!-- Header -->
            <div class="text-center mb-8">
                <div class="w-16 h-16 bg-gradient-to-tr from-blue-600 to-indigo-600 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg">
                    <i class="fas fa-shield-alt text-white text-2xl"></i>
                </div>
                <h1 class="text-3xl font-bold text-gray-900 mb-2">Connexion Admin</h1>
                <p class="text-gray-600">Accédez au Tableau de Bord FAQ Winplus</p>
            </div>

            <!-- Login Form -->
            <form id="loginForm" class="space-y-6">
                <div>
                    <label for="username" class="block text-sm font-semibold text-gray-700 mb-2">
                        <i class="fas fa-user mr-2 text-blue-600"></i>Nom d'utilisateur
                    </label>
                    <input type="text" id="username" name="username" required
                        class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                        placeholder="Entrez votre nom d'utilisateur">
                </div>

                <div>
                    <label for="password" class="block text-sm font-semibold text-gray-700 mb-2">
                        <i class="fas fa-lock mr-2 text-blue-600"></i>Mot de passe
                    </label>
                    <div class="relative">
                        <input type="password" id="password" name="password" required
                            class="w-full px-4 py-3 pr-12 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                            placeholder="Entrez votre mot de passe">
                        <button type="button" id="togglePassword"
                            class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                </div>

                <!-- Error Message -->
                <div id="errorMessage" class="hidden bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-xl">
                    <div class="flex items-center">
                        <i class="fas fa-exclamation-circle mr-2"></i>
                        <span id="errorText">Identifiants invalides. Veuillez réessayer.</span>
                    </div>
                </div>

                <!-- Submit Button -->
                <button type="submit" id="loginBtn"
                    class="w-full flex items-center justify-center space-x-2 px-6 py-3 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-xl font-semibold hover:shadow-lg hover:scale-105 transition-all">
                    <i class="fas fa-sign-in-alt"></i>
                    <span id="loginBtnText">Se Connecter</span>
                </button>
            </form>

            <!-- Back to FAQ -->
            <div class="text-center mt-8 pt-6 border-t border-gray-200">
                <a href="index.html" class="inline-flex items-center space-x-2 text-blue-600 hover:text-blue-700 transition-colors">
                    <i class="fas fa-arrow-left"></i>
                    <span>Retour au FAQ</span>
                </a>
            </div>
        </div>

        <!-- Info Card -->
        <div class="mt-8 bg-white/50 backdrop-blur-xl rounded-2xl p-6 border border-gray-200/50 text-center">
            <div class="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center mx-auto mb-4">
                <i class="fas fa-info-circle text-blue-600 text-xl"></i>
            </div>
            <h3 class="font-semibold text-gray-900 mb-2">Accès Administrateur</h3>
            <p class="text-gray-600 text-sm">
                Ce tableau de bord permet aux administrateurs de visualiser et gérer les soumissions FAQ,
                exporter les données et surveiller les questions des utilisateurs pour la plateforme Winplus.
            </p>
        </div>
    </div>

    <script src="js/auth.js"></script>
</body>
</html>