pipeline {
    agent any
    environment {
       WINPLUS_FAQ_IMAGE = 'winplus_faq'
    }

    stages {

        // delete old container after stopping it
        stage('stop and delete container'){
            steps {
                script {
                    sh "docker stop winplus_faq || true"
                    sh "docker rm winplus_faq || true"
                }
            }
        }


        stage('Clean Up Old Images') {
            steps {
                script {
                    // Remove images that start with 'winproduits-delta:V'
                    sh """
                        docker images -q '${WINPLUS_FAQ_IMAGE}:v1' | xargs -r docker rmi -f
                    """
                }
            }
        }
        stage('Build New Image') {
            steps {
                script {
                    // Build the new Docker image
                    sh "docker build -t ${WINPLUS_FAQ_IMAGE}:v1 ."
                }
            }
        }


        stage('Start New Container') {
                steps {
                    script {
                        def containerName = 'winplus_faq'
                        // Start the new container with volume mount
                        sh """
                            docker run -d --volume winplusFAQ_data.prod:/app/data --volume winplusFAQ_backups.prod:/app/backups --network=backend.preprod --name ${containerName} ${WINPLUS_FAQ_IMAGE}:v1 
                        """
                    }
                }
        }
        }
}