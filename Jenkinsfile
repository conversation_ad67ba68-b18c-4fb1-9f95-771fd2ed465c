pipeline {
    agent any
    environment {
       WINPLUS_FAQ_IMAGE = 'winplus_faq'
    }

    stages {

        // delete old container after stopping it
        stage('stop and delete container'){
            steps {
                script {
                    sh "docker stop winplus_faq || true"
                    sh "docker rm winplus_faq || true"
                }
            }
        }


        stage('Clean Up Old Images') {
            steps {
                script {
                    // Remove images that start with 'winproduits-delta:V'
                    sh """
                        docker images -q '${WINPLUS_FAQ_IMAGE}:v1' | xargs -r docker rmi -f
                    """
                }
            }
        }
        stage('Build New Image') {
            steps {
                script {
                    // Build the new Docker image
                    sh "docker build -t ${WINPLUS_FAQ_IMAGE}:v1 ."
                }
            }
        }


        stage('Prepare Host Directories') {
            steps {
                script {
                    echo "📁 Preparing host directories for volumes..."
                    sh '''
                        # Créer les répertoires sur l'hôte s'ils n'existent pas
                        mkdir -p $(pwd)/data
                        mkdir -p $(pwd)/backups
                        mkdir -p $(pwd)/logs

                        # Définir les permissions appropriées
                        chmod 755 $(pwd)/data
                        chmod 755 $(pwd)/backups
                        chmod 755 $(pwd)/logs

                        echo "✅ Host directories prepared"
                    '''
                }
            }
        }

        stage('Start New Container') {
                steps {
                    script {
                        def containerName = 'winplus_faq'
                        // Start the new container with proper configuration
                        sh """
                            docker run -d \
                                --network=backend.preprod \
                                --name ${containerName} \
                                -p 3000:3000 \
                                -e NODE_ENV=production \
                                -v \$(pwd)/data:/app/data \
                                -v \$(pwd)/backups:/app/backups \
                                -v \$(pwd)/logs:/app/logs \
                                --restart unless-stopped \
                                ${WINPLUS_FAQ_IMAGE}:v1
                        """

                        // Wait for container to start and show logs
                        sh """
                            sleep 10
                            echo "📋 Container Status:"
                            docker ps --filter name=${containerName}
                            echo "📝 Container Logs:"
                            docker logs ${containerName}
                        """
                    }
                }
        }
        }
}