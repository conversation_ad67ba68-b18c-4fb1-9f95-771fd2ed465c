const sqlite3 = require('sqlite3').verbose();
const bcrypt = require('bcryptjs');
const path = require('path');
const fs = require('fs');

// Ensure data directory exists
const dataDir = path.join(__dirname, '..', 'data');
if (!fs.existsSync(dataDir)) {
    fs.mkdirSync(dataDir, { recursive: true });
    console.log('Created data directory');
}

const dbPath = path.join(dataDir, 'faq.db');
const db = new sqlite3.Database(dbPath);

console.log('🗄️  Initializing Winplus FAQ Database...');

db.serialize(() => {
    // Drop existing tables if they exist (for fresh start)
    db.run('DROP TABLE IF EXISTS questions');
    db.run('DROP TABLE IF EXISTS admin_users');
    
    console.log('📋 Creating questions table...');
    db.run(`
        CREATE TABLE questions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_name TEXT NOT NULL,
            user_email TEXT NOT NULL,
            user_phone TEXT,
            user_company TEXT,
            question_text TEXT NOT NULL,
            status TEXT DEFAULT 'pending' CHECK(status IN ('pending', 'reviewed', 'answered', 'archived')),
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
    `, (err) => {
        if (err) {
            console.error('❌ Error creating questions table:', err);
        } else {
            console.log('✅ Questions table created successfully');
        }
    });

    console.log('👤 Creating admin users table...');
    db.run(`
        CREATE TABLE admin_users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT UNIQUE NOT NULL,
            password_hash TEXT NOT NULL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
    `, (err) => {
        if (err) {
            console.error('❌ Error creating admin_users table:', err);
        } else {
            console.log('✅ Admin users table created successfully');
            
            // Create default admin user
            const defaultUsername = 'admin';
            const defaultPassword = 'Sophatel@9999';
            
            console.log('🔐 Creating default admin user...');
            bcrypt.hash(defaultPassword, 10, (err, hash) => {
                if (err) {
                    console.error('❌ Error hashing password:', err);
                    return;
                }
                
                db.run(`
                    INSERT INTO admin_users (username, password_hash) 
                    VALUES (?, ?)
                `, [defaultUsername, hash], function(err) {
                    if (err) {
                        console.error('❌ Error creating admin user:', err);
                    } else {
                        console.log('✅ Default admin user created successfully');
                        console.log(`   Username: ${defaultUsername}`);
                        console.log(`   Password: ${defaultPassword}`);
                    }
                });
            });
        }
    });

    // Insert some sample questions for testing
    console.log('📝 Adding sample questions...');
    
    const sampleQuestions = [
        {
            user_name: 'Dr. Ahmed Hassan',
            user_email: '<EMAIL>',
            user_phone: '******-0123',
            user_company: 'Hassan Pharmacy',
            question_text: 'How does the inventory management system handle expired medications? Can it automatically alert us when products are approaching their expiration dates?',
            status: 'pending'
        },
        {
            user_name: 'Sarah Johnson',
            user_email: '<EMAIL>',
            user_phone: '******-0456',
            user_company: 'MedStore Plus',
            question_text: 'What are the insurance billing capabilities? Can Winplus handle multiple TPA providers and automatically process claims?',
            status: 'reviewed'
        },
        {
            user_name: 'Mohamed Ali',
            user_email: '<EMAIL>',
            user_phone: '******-0789',
            user_company: 'CityCare Pharmacy',
            question_text: 'How does the credit management system work for suppliers? Can we set different payment terms for different suppliers?',
            status: 'answered'
        },
        {
            user_name: 'Lisa Chen',
            user_email: '<EMAIL>',
            user_phone: null,
            user_company: 'HealthPlus Pharmacy',
            question_text: 'Does Winplus support barcode scanning for faster product entry and sales processing?',
            status: 'pending'
        },
        {
            user_name: 'Dr. Omar Khalil',
            user_email: '<EMAIL>',
            user_phone: '******-0321',
            user_company: null,
            question_text: 'What kind of reports and analytics does the system provide? Can we track sales by product category, supplier, or time period?',
            status: 'pending'
        }
    ];

    sampleQuestions.forEach((question, index) => {
        db.run(`
            INSERT INTO questions (user_name, user_email, user_phone, user_company, question_text, status)
            VALUES (?, ?, ?, ?, ?, ?)
        `, [
            question.user_name,
            question.user_email,
            question.user_phone,
            question.user_company,
            question.question_text,
            question.status
        ], function(err) {
            if (err) {
                console.error(`❌ Error inserting sample question ${index + 1}:`, err);
            } else {
                console.log(`✅ Sample question ${index + 1} added (ID: ${this.lastID})`);
            }
        });
    });
});

db.close((err) => {
    if (err) {
        console.error('❌ Error closing database:', err);
    } else {
        console.log('\n🎉 Database initialization completed successfully!');
        console.log('\n📊 You can now start the server with: npm start');
        console.log('🌐 Access the FAQ form at: http://localhost:3000');
        console.log('🔧 Access the admin dashboard at: http://localhost:3000/admin');
        console.log('🔐 Admin credentials: admin / Sophatel@9999');
    }
});
