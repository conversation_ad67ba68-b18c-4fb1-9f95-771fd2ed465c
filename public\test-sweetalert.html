<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test SweetAlert</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-md mx-auto bg-white rounded-lg shadow-md p-6">
        <h1 class="text-2xl font-bold mb-4">Test SweetAlert</h1>
        
        <button id="testSuccess" class="bg-green-500 text-white px-4 py-2 rounded mr-2 mb-2">
            Test Succès
        </button>
        
        <button id="testError" class="bg-red-500 text-white px-4 py-2 rounded mr-2 mb-2">
            Test Erreur
        </button>
        
        <button id="testCheck" class="bg-blue-500 text-white px-4 py-2 rounded mr-2 mb-2">
            Vérifier SweetAlert
        </button>
        
        <div id="status" class="mt-4 p-3 rounded"></div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const statusDiv = document.getElementById('status');
            
            // Vérifier le chargement de SweetAlert
            function checkSweetAlert() {
                if (typeof Swal !== 'undefined') {
                    statusDiv.className = 'mt-4 p-3 rounded bg-green-100 text-green-800';
                    statusDiv.textContent = '✅ SweetAlert2 est chargé et prêt !';
                    return true;
                } else {
                    statusDiv.className = 'mt-4 p-3 rounded bg-red-100 text-red-800';
                    statusDiv.textContent = '❌ SweetAlert2 n\'est pas chargé !';
                    return false;
                }
            }
            
            // Test initial
            checkSweetAlert();
            
            // Test succès
            document.getElementById('testSuccess').addEventListener('click', function() {
                if (checkSweetAlert()) {
                    Swal.fire({
                        icon: 'success',
                        title: 'Test Succès !',
                        text: 'SweetAlert fonctionne parfaitement !',
                        confirmButtonText: 'Parfait !',
                        confirmButtonColor: '#1a73e8',
                        timer: 3000,
                        timerProgressBar: true,
                        showClass: {
                            popup: 'animate__animated animate__fadeInDown'
                        },
                        hideClass: {
                            popup: 'animate__animated animate__fadeOutUp'
                        }
                    });
                }
            });
            
            // Test erreur
            document.getElementById('testError').addEventListener('click', function() {
                if (checkSweetAlert()) {
                    Swal.fire({
                        icon: 'error',
                        title: 'Test Erreur !',
                        text: 'Ceci est un test d\'erreur avec animation shake.',
                        confirmButtonText: 'Réessayer',
                        confirmButtonColor: '#ea4335',
                        showClass: {
                            popup: 'animate__animated animate__shakeX'
                        },
                        hideClass: {
                            popup: 'animate__animated animate__fadeOutUp'
                        }
                    });
                }
            });
            
            // Vérification manuelle
            document.getElementById('testCheck').addEventListener('click', function() {
                checkSweetAlert();
                console.log('SweetAlert check:', typeof Swal);
                console.log('Window.Swal:', window.Swal);
            });
        });
    </script>
</body>
</html>
