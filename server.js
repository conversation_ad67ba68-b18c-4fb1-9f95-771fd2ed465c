const express = require('express');
const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const cors = require('cors');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
const bcrypt = require('bcryptjs');
const session = require('express-session');
const SQLiteStore = require('connect-sqlite3')(session);
const ExcelJS = require('exceljs');
const { body, validationResult } = require('express-validator');
const compression = require('compression');
const morgan = require('morgan');

const app = express();
const PORT = process.env.PORT || 3000;

// Database setup
const dbPath = path.join(__dirname, 'data', 'faq.db');
const db = new sqlite3.Database(dbPath);

// Middleware
app.use(helmet({
    contentSecurityPolicy: {
        directives: {
            defaultSrc: ["'self'"],
            styleSrc: ["'self'", "'unsafe-inline'", "https://cdn.tailwindcss.com", "https://cdnjs.cloudflare.com", "https://fonts.googleapis.com"],
            scriptSrc: ["'self'", "'unsafe-inline'", "https://cdn.tailwindcss.com"],
            fontSrc: ["'self'", "https://fonts.gstatic.com", "https://cdnjs.cloudflare.com"],
            imgSrc: ["'self'", "data:", "https:"],
            connectSrc: ["'self'"]
        }
    }
}));

app.use(compression());
app.use(morgan('combined'));
app.use(cors());
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Rate limiting
const limiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100, // limit each IP to 100 requests per windowMs
    message: 'Too many requests from this IP, please try again later.'
});
app.use(limiter);

// Session configuration
app.use(session({
    store: new SQLiteStore({
        db: 'sessions.db',
        dir: path.join(__dirname, 'data')
    }),
    secret: 'winplus-faq-secret-key-change-in-production',
    resave: false,
    saveUninitialized: false,
    cookie: {
        secure: false, // Set to true in production with HTTPS
        httpOnly: true,
        maxAge: 24 * 60 * 60 * 1000 // 24 hours
    }
}));

// Static files
app.use(express.static('public'));

// Initialize database
function initDatabase() {
    return new Promise((resolve, reject) => {
        db.serialize(() => {
            // Create questions table
            db.run(`
                CREATE TABLE IF NOT EXISTS questions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_name TEXT NOT NULL,
                    user_email TEXT,
                    user_phone TEXT,
                    user_company TEXT,
                    user_function TEXT,
                    question_text TEXT NOT NULL,
                    status TEXT DEFAULT 'pending',
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            `, (err) => {
                if (err) {
                    console.error('Error creating questions table:', err);
                    reject(err);
                } else {
                    console.log('Questions table created or already exists');
                }
            });

            // Create admin users table (simple static admin for now)
            db.run(`
                CREATE TABLE IF NOT EXISTS admin_users (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    username TEXT UNIQUE NOT NULL,
                    password_hash TEXT NOT NULL,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            `, (err) => {
                if (err) {
                    console.error('Error creating admin_users table:', err);
                    reject(err);
                } else {
                    console.log('Admin users table created or already exists');
                    
                    // Insert default admin user if not exists
                    const defaultPassword = 'Sophatel@9999';
                    bcrypt.hash(defaultPassword, 10, (err, hash) => {
                        if (err) {
                            console.error('Error hashing password:', err);
                            reject(err);
                            return;
                        }
                        
                        db.run(`
                            INSERT OR IGNORE INTO admin_users (username, password_hash) 
                            VALUES (?, ?)
                        `, ['admin', hash], (err) => {
                            if (err) {
                                console.error('Error inserting admin user:', err);
                                reject(err);
                            } else {
                                console.log('Default admin user created or already exists');
                                resolve();
                            }
                        });
                    });
                }
            });
        });
    });
}

// Validation middleware
const validateQuestion = [
    body('user_name').trim().isLength({ min: 2, max: 100 }).escape(),
    body('user_email').custom((value) => {
        // Si vide ou undefined, c'est valide (optionnel)
        if (!value || value.trim() === '') {
            return true;
        }
        // Si fourni, doit être un email valide
        if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
            throw new Error('Format email invalide');
        }
        return true;
    }).normalizeEmail(),
    body('user_phone').optional().trim().isLength({ max: 20 }).escape(),
    body('user_company').optional().trim().isLength({ max: 100 }).escape(),
    body('user_function').trim().isLength({ min: 1 }).withMessage('La fonction est requise').isIn(['pharmacien', 'commercial', 'team_sophatel', 'autres']).withMessage('Fonction invalide'),
    body('questions').isArray({ min: 1, max: 50 }),
    body('questions.*').trim().isLength({ min: 10, max: 1000 }).escape()
];

const validateLogin = [
    body('username').trim().isLength({ min: 1, max: 50 }).escape(),
    body('password').isLength({ min: 1, max: 100 })
];

// Authentication middleware
function requireAuth(req, res, next) {
    if (req.session && req.session.adminId) {
        next();
    } else {
        res.status(401).json({ success: false, message: 'Authentication required' });
    }
}

// Routes

// Serve main pages
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

app.get('/admin', (req, res) => {
    if (req.session && req.session.adminId) {
        res.sendFile(path.join(__dirname, 'public', 'admin.html'));
    } else {
        res.redirect('/login.html');
    }
});

app.get('/login', (req, res) => {
    res.redirect('/login.html');
});

// Health check endpoint
app.get('/api/health', (req, res) => {
    res.json({
        success: true,
        message: 'Winplus FAQ Server is running',
        timestamp: new Date().toISOString(),
        version: '1.0.0'
    });
});

// API Routes

// Submit questions
app.post('/api/questions', validateQuestion, (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        // Créer des messages d'erreur spécifiques
        const errorMessages = errors.array().map(error => {
            const fieldNames = {
                'user_name': 'Nom complet',
                'user_email': 'Adresse email',
                'user_phone': 'Numéro de téléphone',
                'user_company': 'Nom de la pharmacie/entreprise',
                'user_function': 'Fonction',
                'questions': 'Questions',
                'questions.*': 'Question'
            };

            const fieldName = fieldNames[error.path] || error.path;
            let message = error.msg;

            // Messages personnalisés selon le type d'erreur
            if (error.msg === 'Invalid value' && error.path === 'user_email') {
                message = 'Format email invalide';
            } else if (error.msg === 'Invalid value' && error.path === 'user_function') {
                message = 'Veuillez sélectionner une fonction';
            } else if (error.path.includes('questions')) {
                if (error.msg.includes('min')) {
                    message = 'La question doit contenir au moins 10 caractères';
                } else if (error.msg.includes('max')) {
                    message = 'La question ne peut pas dépasser 1000 caractères';
                }
            }

            return `${fieldName}: ${message}`;
        });

        return res.status(400).json({
            success: false,
            message: 'Erreurs de validation détectées',
            errors: errors.array(),
            detailedErrors: errorMessages
        });
    }

    const { user_name, user_email, user_phone, user_company, user_function, questions } = req.body;

    // Insert each question separately
    const insertPromises = questions.map(question => {
        return new Promise((resolve, reject) => {
            db.run(`
                INSERT INTO questions (user_name, user_email, user_phone, user_company, user_function, question_text)
                VALUES (?, ?, ?, ?, ?, ?)
            `, [user_name, user_email || null, user_phone || null, user_company || null, user_function || null, question], function(err) {
                if (err) {
                    reject(err);
                } else {
                    resolve(this.lastID);
                }
            });
        });
    });

    Promise.all(insertPromises)
        .then(ids => {
            res.json({
                success: true,
                message: 'Questions submitted successfully',
                questionIds: ids
            });
        })
        .catch(err => {
            console.error('Error inserting questions:', err);
            res.status(500).json({
                success: false,
                message: 'Failed to submit questions'
            });
        });
});

// Admin login
app.post('/api/admin/login', validateLogin, (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        return res.status(400).json({
            success: false,
            message: 'Validation failed'
        });
    }

    const { username, password } = req.body;

    db.get(`
        SELECT id, username, password_hash FROM admin_users WHERE username = ?
    `, [username], (err, user) => {
        if (err) {
            console.error('Database error:', err);
            return res.status(500).json({
                success: false,
                message: 'Internal server error'
            });
        }

        if (!user) {
            return res.status(401).json({
                success: false,
                message: 'Invalid credentials'
            });
        }

        bcrypt.compare(password, user.password_hash, (err, isMatch) => {
            if (err) {
                console.error('Password comparison error:', err);
                return res.status(500).json({
                    success: false,
                    message: 'Internal server error'
                });
            }

            if (isMatch) {
                req.session.adminId = user.id;
                req.session.username = user.username;
                res.json({
                    success: true,
                    message: 'Login successful'
                });
            } else {
                res.status(401).json({
                    success: false,
                    message: 'Invalid credentials'
                });
            }
        });
    });
});

// Admin logout
app.post('/api/admin/logout', (req, res) => {
    req.session.destroy((err) => {
        if (err) {
            console.error('Session destruction error:', err);
        }
        res.json({ success: true, message: 'Logged out successfully' });
    });
});

// Get all questions (admin only)
app.get('/api/admin/questions', requireAuth, (req, res) => {
    db.all(`
        SELECT * FROM questions 
        ORDER BY created_at DESC
    `, (err, questions) => {
        if (err) {
            console.error('Error fetching questions:', err);
            return res.status(500).json({
                success: false,
                message: 'Failed to fetch questions'
            });
        }

        res.json({
            success: true,
            questions: questions
        });
    });
});

// Update question status (admin only)
app.put('/api/admin/questions/:id/status', requireAuth, (req, res) => {
    const { id } = req.params;
    const { status } = req.body;

    const validStatuses = ['pending', 'reviewed', 'answered', 'archived'];
    if (!validStatuses.includes(status)) {
        return res.status(400).json({
            success: false,
            message: 'Invalid status'
        });
    }

    db.run(`
        UPDATE questions 
        SET status = ?, updated_at = CURRENT_TIMESTAMP 
        WHERE id = ?
    `, [status, id], function(err) {
        if (err) {
            console.error('Error updating question status:', err);
            return res.status(500).json({
                success: false,
                message: 'Failed to update status'
            });
        }

        if (this.changes === 0) {
            return res.status(404).json({
                success: false,
                message: 'Question not found'
            });
        }

        res.json({
            success: true,
            message: 'Status updated successfully'
        });
    });
});

// Bulk delete questions (admin only) - DOIT ÊTRE AVANT la route avec :id
app.delete('/api/admin/questions/bulk-delete', requireAuth, (req, res) => {
    console.log('🗑️ BULK DELETE endpoint called');
    console.log('Request body:', req.body);
    const { questionIds } = req.body;

    if (!Array.isArray(questionIds) || questionIds.length === 0) {
        return res.status(400).json({
            success: false,
            message: 'Question IDs array is required'
        });
    }

    // Create placeholders for the IN clause
    const placeholders = questionIds.map(() => '?').join(',');
    const query = `DELETE FROM questions WHERE id IN (${placeholders})`;

    db.run(query, questionIds, function(err) {
        if (err) {
            console.error('Error bulk deleting questions:', err);
            return res.status(500).json({
                success: false,
                message: 'Failed to delete questions'
            });
        }

        res.json({
            success: true,
            message: `${this.changes} questions deleted successfully`,
            deletedCount: this.changes
        });
    });
});

// Bulk update question status (admin only)
app.put('/api/admin/questions/bulk-status', requireAuth, (req, res) => {
    const { questionIds, status } = req.body;

    if (!Array.isArray(questionIds) || questionIds.length === 0) {
        return res.status(400).json({
            success: false,
            message: 'Question IDs array is required'
        });
    }

    if (!['pending', 'reviewed', 'answered', 'archived'].includes(status)) {
        return res.status(400).json({
            success: false,
            message: 'Invalid status value'
        });
    }

    // Create placeholders for the IN clause
    const placeholders = questionIds.map(() => '?').join(',');
    const query = `UPDATE questions SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE id IN (${placeholders})`;

    db.run(query, [status, ...questionIds], function(err) {
        if (err) {
            console.error('Error bulk updating question status:', err);
            return res.status(500).json({
                success: false,
                message: 'Failed to update question status'
            });
        }

        res.json({
            success: true,
            message: `${this.changes} questions updated successfully`,
            updatedCount: this.changes
        });
    });
});

// Delete question (admin only) - APRÈS les routes bulk
app.delete('/api/admin/questions/:id', requireAuth, (req, res) => {
    const { id } = req.params;

    db.run(`DELETE FROM questions WHERE id = ?`, [id], function(err) {
        if (err) {
            console.error('Error deleting question:', err);
            return res.status(500).json({
                success: false,
                message: 'Failed to delete question'
            });
        }

        if (this.changes === 0) {
            return res.status(404).json({
                success: false,
                message: 'Question not found'
            });
        }

        res.json({
            success: true,
            message: 'Question deleted successfully'
        });
    });
});

// Export questions to Excel (admin only)
app.get('/api/admin/export', requireAuth, async (req, res) => {
    try {
        const questions = await new Promise((resolve, reject) => {
            db.all(`
                SELECT * FROM questions
                ORDER BY created_at DESC
            `, (err, rows) => {
                if (err) reject(err);
                else resolve(rows);
            });
        });

        // Create Excel workbook
        const workbook = new ExcelJS.Workbook();
        const worksheet = workbook.addWorksheet('FAQ Questions');

        // Define columns
        worksheet.columns = [
            { header: 'ID', key: 'id', width: 10 },
            { header: 'Name', key: 'user_name', width: 20 },
            { header: 'Email', key: 'user_email', width: 30 },
            { header: 'Phone', key: 'user_phone', width: 15 },
            { header: 'Company', key: 'user_company', width: 25 },
            { header: 'Question', key: 'question_text', width: 50 },
            { header: 'Status', key: 'status', width: 15 },
            { header: 'Created At', key: 'created_at', width: 20 },
            { header: 'Updated At', key: 'updated_at', width: 20 }
        ];

        // Style the header row
        worksheet.getRow(1).font = { bold: true };
        worksheet.getRow(1).fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: 'FFE0F2FE' }
        };

        // Add data rows
        questions.forEach(question => {
            worksheet.addRow(question);
        });

        // Auto-fit columns
        worksheet.columns.forEach(column => {
            if (column.key === 'question_text') {
                column.width = 50;
            }
        });

        // Set response headers
        res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        res.setHeader('Content-Disposition', 'attachment; filename=winplus-faq-questions.xlsx');

        // Write to response
        await workbook.xlsx.write(res);
        res.end();

    } catch (error) {
        console.error('Export error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to export questions'
        });
    }
});

// Database backup endpoint
app.post('/api/admin/backup', requireAuth, async (req, res) => {
    try {
        const fs = require('fs');
        const path = require('path');

        console.log('🔄 Starting database backup...');

        // Check if source database exists
        const sourceDbPath = path.join(__dirname, 'data', 'faq.db');
        console.log('📁 Source DB path:', sourceDbPath);

        if (!fs.existsSync(sourceDbPath)) {
            console.error('❌ Source database not found:', sourceDbPath);
            return res.status(500).json({
                success: false,
                message: 'Base de données source introuvable',
                details: `Fichier non trouvé: ${sourceDbPath}`
            });
        }

        console.log('✅ Source database found, size:', fs.statSync(sourceDbPath).size, 'bytes');

        // Create backup filename with timestamp
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const backupPath = path.join(__dirname, 'backups');
        const backupFile = path.join(backupPath, `winplus-faq-backup-${timestamp}.db`);

        console.log('📁 Backup directory:', backupPath);
        console.log('📄 Backup file:', backupFile);

        // Create backups directory if it doesn't exist
        if (!fs.existsSync(backupPath)) {
            console.log('📁 Creating backups directory...');
            fs.mkdirSync(backupPath, { recursive: true });
            console.log('✅ Backups directory created');
        } else {
            console.log('✅ Backups directory already exists');
        }

        // Copy database file
        console.log('📋 Copying database file...');
        fs.copyFileSync(sourceDbPath, backupFile);

        const backupSize = fs.statSync(backupFile).size;
        console.log('✅ Backup completed successfully, size:', backupSize, 'bytes');

        res.json({
            success: true,
            message: 'Sauvegarde créée avec succès',
            filename: `winplus-faq-backup-${timestamp}.db`,
            size: backupSize,
            sourcePath: sourceDbPath,
            backupPath: backupFile
        });

    } catch (error) {
        console.error('❌ Backup error:', error);
        console.error('Error details:', {
            message: error.message,
            code: error.code,
            errno: error.errno,
            path: error.path
        });

        res.status(500).json({
            success: false,
            message: 'Erreur lors de la sauvegarde',
            error: error.message,
            details: error.code || 'Unknown error'
        });
    }
});

// Clear cache endpoint
app.post('/api/admin/clear-cache', requireAuth, (req, res) => {
    try {
        // In a real application, you would clear actual cache here
        // For now, we'll simulate cache clearing

        // Clear any in-memory cache if you have one
        // Example: cache.clear();

        res.json({
            success: true,
            message: 'Cache nettoyé avec succès',
            clearedItems: Math.floor(Math.random() * 100) + 50 // Simulate cleared items
        });

    } catch (error) {
        console.error('Cache clear error:', error);
        res.status(500).json({ success: false, message: 'Erreur lors du nettoyage du cache' });
    }
});

// System info endpoint
app.get('/api/admin/system-info', requireAuth, (req, res) => {
    try {
        const fs = require('fs');
        const path = require('path');

        // Get database size
        const dbPath = path.join(__dirname, 'data', 'faq.db');
        const dbSize = fs.existsSync(dbPath) ? fs.statSync(dbPath).size : 0;

        // Get system info
        const systemInfo = {
            nodeVersion: process.version,
            uptime: Math.floor(process.uptime()),
            memoryUsage: process.memoryUsage(),
            dbSize: dbSize,
            platform: process.platform,
            arch: process.arch
        };

        res.json({ success: true, data: systemInfo });

    } catch (error) {
        console.error('System info error:', error);
        res.status(500).json({ success: false, message: 'Erreur lors de la récupération des informations système' });
    }
});

// Test backup endpoint (sans authentification pour diagnostic)
app.post('/api/test/backup', async (req, res) => {
    try {
        const fs = require('fs');
        const path = require('path');

        console.log('🧪 TEST: Starting database backup...');

        // Check if source database exists
        const sourceDbPath = path.join(__dirname, 'data', 'faq.db');
        console.log('📁 TEST: Source DB path:', sourceDbPath);

        if (!fs.existsSync(sourceDbPath)) {
            console.error('❌ TEST: Source database not found:', sourceDbPath);
            return res.status(500).json({
                success: false,
                message: 'Base de données source introuvable',
                details: `Fichier non trouvé: ${sourceDbPath}`
            });
        }

        console.log('✅ TEST: Source database found, size:', fs.statSync(sourceDbPath).size, 'bytes');

        // Create backup filename with timestamp
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const backupPath = path.join(__dirname, 'backups');
        const backupFile = path.join(backupPath, `test-backup-${timestamp}.db`);

        console.log('📁 TEST: Backup directory:', backupPath);
        console.log('📄 TEST: Backup file:', backupFile);

        // Create backups directory if it doesn't exist
        if (!fs.existsSync(backupPath)) {
            console.log('📁 TEST: Creating backups directory...');
            fs.mkdirSync(backupPath, { recursive: true });
            console.log('✅ TEST: Backups directory created');
        } else {
            console.log('✅ TEST: Backups directory already exists');
        }

        // Copy database file
        console.log('📋 TEST: Copying database file...');
        fs.copyFileSync(sourceDbPath, backupFile);

        const backupSize = fs.statSync(backupFile).size;
        console.log('✅ TEST: Backup completed successfully, size:', backupSize, 'bytes');

        res.json({
            success: true,
            message: 'TEST: Sauvegarde créée avec succès',
            filename: `test-backup-${timestamp}.db`,
            size: backupSize,
            sourcePath: sourceDbPath,
            backupPath: backupFile,
            note: 'Ceci est un test sans authentification'
        });

    } catch (error) {
        console.error('❌ TEST: Backup error:', error);
        console.error('TEST: Error details:', {
            message: error.message,
            code: error.code,
            errno: error.errno,
            path: error.path
        });

        res.status(500).json({
            success: false,
            message: 'TEST: Erreur lors de la sauvegarde',
            error: error.message,
            details: error.code || 'Unknown error'
        });
    }
});

// Error handling middleware
app.use((err, req, res, next) => {
    console.error('Unhandled error:', err);
    res.status(500).json({
        success: false,
        message: 'Internal server error'
    });
});

// 404 handler
app.use((req, res) => {
    res.status(404).json({
        success: false,
        message: 'Endpoint not found'
    });
});

// Start server
async function startServer() {
    try {
        // Ensure data directory exists
        const fs = require('fs');
        const dataDir = path.join(__dirname, 'data');
        if (!fs.existsSync(dataDir)) {
            fs.mkdirSync(dataDir, { recursive: true });
        }

        // Initialize database
        await initDatabase();

        // Start listening
        app.listen(PORT, () => {
            console.log(`🚀 Winplus FAQ Server running on http://localhost:${PORT}`);
            console.log(`📊 Admin Dashboard: http://localhost:${PORT}/admin`);
            console.log(`🔐 Admin Login: admin / Sophatel@9999`);
        });
    } catch (error) {
        console.error('Failed to start server:', error);
        process.exit(1);
    }
}

// Graceful shutdown
process.on('SIGINT', () => {
    console.log('\n🛑 Shutting down server...');
    db.close((err) => {
        if (err) {
            console.error('Error closing database:', err);
        } else {
            console.log('Database connection closed.');
        }
        process.exit(0);
    });
});

startServer();
