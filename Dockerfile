# Multi-stage build pour optimiser la taille de l'image
FROM node:20-alpine AS builder

# Métadonnées
LABEL maintainer="Winplus Pharma <<EMAIL>>"
LABEL description="FAQ Application for Winplus Pharma"
LABEL version="1.0.0"

# Définir le répertoire de travail
WORKDIR /app

# Copier les fichiers de dépendances
COPY package*.json ./

# Installer les dépendances (avec cache optimisé)
RUN npm ci --only=production && npm cache clean --force

# Copier le code source
COPY . .

# Créer les répertoires nécessaires avec les bonnes permissions
RUN mkdir -p /app/data /app/backups /app/logs && \
    chmod -R 755 /app/data /app/backups /app/logs

# Initialiser la base de données
RUN node scripts/init-db.js

# Exposer le port
EXPOSE 3000

# Ajouter un health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD node -e "const http = require('http'); \
    const options = { host: 'localhost', port: 3000, path: '/api/health', timeout: 2000 }; \
    const request = http.request(options, (res) => { \
        if (res.statusCode == 200) process.exit(0); \
        else process.exit(1); \
    }); \
    request.on('error', () => process.exit(1)); \
    request.end();"

# Commande par défaut
CMD ["node", "server.js"]
