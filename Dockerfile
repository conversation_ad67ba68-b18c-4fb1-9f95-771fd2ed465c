# Multi-stage build pour optimiser la taille de l'image
FROM node:20-alpine AS builder

# Métadonnées
LABEL maintainer="Winplus Pharma <<EMAIL>>"
LABEL description="FAQ Application for Winplus Pharma"
LABEL version="1.0.0"

# Définir le répertoire de travail
WORKDIR /app

# Copier les fichiers de dépendances
COPY package*.json ./

# Installer les dépendances (avec cache optimisé)
RUN npm ci --only=production && npm cache clean --force

# Copier le code source
COPY . .

# Créer les répertoires nécessaires
RUN mkdir -p /app/data /app/backups /app/logs

# Exposer le port
EXPOSE 3000

# Commande par défaut - utilise le script de démarrage qui gère l'initialisation/migration
CMD ["node", "scripts/startup.js"]
